/* Global Styles */
* {
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.container-fluid {
    padding: 0;
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 15px;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 600;
}

.sidebar-header p {
    margin: 5px 0 0 0;
    font-size: 0.85rem;
    opacity: 0.8;
}

/* Upload Section */
.upload-section {
    margin-bottom: 30px;
}

.upload-section h6 {
    margin-bottom: 15px;
    font-weight: 600;
    opacity: 0.9;
}

.upload-area {
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.1);
}

.upload-area:hover {
    border-color: rgba(255,255,255,0.6);
    background: rgba(255,255,255,0.15);
}

.upload-area.dragover {
    border-color: #28a745;
    background: rgba(40,167,69,0.2);
}

.upload-area i {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.7;
}

.upload-area p {
    margin: 10px 0 5px 0;
    font-weight: 500;
}

.upload-area small {
    opacity: 0.7;
    font-size: 0.75rem;
}

.upload-progress {
    margin-top: 15px;
}

.upload-progress .progress {
    height: 6px;
    background: rgba(255,255,255,0.2);
}

.upload-progress .progress-bar {
    background: #28a745;
}

/* Documents Section */
.documents-section {
    margin-bottom: 30px;
}

.documents-section h6 {
    margin-bottom: 15px;
    font-weight: 600;
    opacity: 0.9;
}

.documents-list {
    max-height: 200px;
    overflow-y: auto;
}

.document-item {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.document-item:hover {
    background: rgba(255,255,255,0.2);
    transform: translateX(5px);
}

.document-item .doc-name {
    font-weight: 500;
    font-size: 0.85rem;
    margin-bottom: 3px;
}

.document-item .doc-info {
    font-size: 0.7rem;
    opacity: 0.7;
}

/* Stats Section */
.stats-section h6 {
    margin-bottom: 15px;
    font-weight: 600;
    opacity: 0.9;
}

.stats-info {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-size: 0.85rem;
    opacity: 0.8;
}

.stat-value {
    font-weight: 600;
    color: #28a745;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 0;
}

/* Chat Header */
.chat-header {
    background: white;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chat-header h5 {
    margin: 0;
    color: #495057;
}

.chat-controls .btn {
    margin-left: 5px;
}

/* Chat Container */
.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.welcome-message {
    max-width: 600px;
    margin: 50px auto;
}

.example-questions {
    margin-top: 20px;
}

.example-question {
    background: #e9ecef;
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.example-question:hover {
    background: #007bff;
    color: white;
}

/* Chat Messages */
.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
}

.message.user .message-content {
    background: #007bff;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background: white;
    color: #333;
    border: 1px solid #dee2e6;
    border-bottom-left-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
    font-size: 0.9rem;
}

.message.user .message-avatar {
    background: #007bff;
    color: white;
    order: 2;
}

.message.assistant .message-avatar {
    background: #28a745;
    color: white;
}

.message-sources {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #dee2e6;
    font-size: 0.8rem;
    color: #6c757d;
}

.source-badge {
    display: inline-block;
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
    margin: 2px;
    font-size: 0.75rem;
}

.source-badge.documents {
    background: #d4edda;
    color: #155724;
}

.source-badge.web {
    background: #cce5ff;
    color: #004085;
}

.source-badge.gemini {
    background: #fff3cd;
    color: #856404;
}

/* Chat Input */
.chat-input-container {
    background: white;
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
}

.input-group {
    margin-bottom: 8px;
}

.input-help {
    text-align: center;
}

#messageInput {
    border-radius: 25px;
    border: 1px solid #dee2e6;
    padding: 12px 20px;
    font-size: 0.95rem;
}

#messageInput:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

#sendButton {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    border: none;
    background: #007bff;
    margin-left: 10px;
}

#sendButton:hover {
    background: #0056b3;
}

#sendButton:disabled {
    background: #6c757d;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        width: 280px;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .message-content {
        max-width: 85%;
    }
}

/* Scrollbar Styling */
.chat-container::-webkit-scrollbar,
.documents-list::-webkit-scrollbar {
    width: 6px;
}

.chat-container::-webkit-scrollbar-track,
.documents-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb,
.documents-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover,
.documents-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading Animation */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 12px 16px;
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    background: #999;
    border-radius: 50%;
    display: inline-block;
    margin: 0 2px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}
