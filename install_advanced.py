#!/usr/bin/env python3
"""
Script cài đặt advanced dependencies (audio, video, vector DB)
"""

import subprocess
import sys
import os

def run_pip_install(packages, description, optional=False):
    """Cài đặt packages với pip"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    if isinstance(packages, str):
        packages = [packages]
    
    failed_packages = []
    
    for package in packages:
        print(f"Installing {package}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], check=True, capture_output=True, text=True)
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}")
            print(f"Error: {e.stderr}")
            failed_packages.append(package)
            if not optional:
                return False, failed_packages
    
    return True, failed_packages

def install_system_dependencies():
    """Hướng dẫn cài đặt system dependencies"""
    print("\n📋 System Dependencies Required:")
    print("For audio/video processing, you may need:")
    print("\nWindows:")
    print("- Install Microsoft Visual C++ Build Tools")
    print("- Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/")
    print("\nLinux (Ubuntu/Debian):")
    print("sudo apt update")
    print("sudo apt install -y build-essential ffmpeg libsm6 libxext6 libfontconfig1 libxrender1")
    print("\nMacOS:")
    print("brew install ffmpeg")
    print("\nPress Enter to continue after installing system dependencies...")
    input()

def main():
    """Main installation function"""
    print("🚀 FastAPI Gemini AI Agent - Advanced Features Installer")
    
    # Check if core dependencies are installed
    try:
        import fastapi
        import google.generativeai
        print("✅ Core dependencies found")
    except ImportError as e:
        print(f"❌ Core dependencies missing: {e}")
        print("Please run: python install_dependencies.py first")
        return False
    
    # Ask about system dependencies
    response = input("\nHave you installed system dependencies for audio/video? (y/n): ")
    if response.lower() != 'y':
        install_system_dependencies()
    
    # Install PDF processing
    success, failed = run_pip_install("pdfplumber", "Installing advanced PDF processing", optional=True)
    
    # Install image processing
    success, failed = run_pip_install("opencv-python-headless", "Installing image processing", optional=True)
    if failed:
        print("⚠️ OpenCV failed, trying alternative...")
        run_pip_install("opencv-python", "Installing OpenCV (with GUI)", optional=True)
    
    # Install audio processing
    audio_packages = ["pydub", "SpeechRecognition"]
    success, failed = run_pip_install(audio_packages, "Installing audio processing", optional=True)
    
    # Install Whisper (optional, large download)
    response = input("\nInstall OpenAI Whisper for better audio transcription? (y/n): ")
    if response.lower() == 'y':
        run_pip_install("openai-whisper", "Installing OpenAI Whisper", optional=True)
    
    # Install video processing
    print("\n⚠️ MoviePy installation may take a while and require system dependencies...")
    response = input("Install MoviePy for video processing? (y/n): ")
    if response.lower() == 'y':
        run_pip_install("moviepy", "Installing video processing", optional=True)
    
    # Install vector database
    print("\n📊 Installing vector database (ChromaDB)...")
    success, failed = run_pip_install("chromadb", "Installing ChromaDB", optional=True)
    
    if not failed:
        run_pip_install("sentence-transformers", "Installing sentence transformers", optional=True)
    
    # Install testing dependencies
    response = input("\nInstall testing dependencies? (y/n): ")
    if response.lower() == 'y':
        test_packages = ["pytest", "pytest-asyncio", "pytest-cov"]
        run_pip_install(test_packages, "Installing testing dependencies", optional=True)
    
    print("\n🎉 Advanced installation completed!")
    print("\n📋 What's installed:")
    
    # Check what's available
    features = {
        "PDF Processing": ["PyPDF2", "pdfplumber"],
        "Image Processing": ["PIL", "cv2"],
        "Audio Processing": ["pydub", "speech_recognition"],
        "Video Processing": ["moviepy"],
        "Vector Database": ["chromadb", "sentence_transformers"],
        "Testing": ["pytest"]
    }
    
    for feature, modules in features.items():
        available = []
        for module in modules:
            try:
                __import__(module)
                available.append(module)
            except ImportError:
                pass
        
        if available:
            print(f"✅ {feature}: {', '.join(available)}")
        else:
            print(f"❌ {feature}: Not available")
    
    print("\n🚀 Ready to run:")
    print("python run.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
