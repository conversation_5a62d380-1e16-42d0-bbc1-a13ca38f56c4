import requests
from bs4 import BeautifulSoup
import httpx
from typing import Dict, List, Optional, Any
import logging
import asyncio
import json
from urllib.parse import quote_plus, urljoin
import re

from app.core.config import settings

logger = logging.getLogger(__name__)


class WebSearchService:
    """Service để tìm kiếm thông tin trên web"""
    
    def __init__(self):
        """Khởi tạo web search service"""
        self.google_api_key = settings.google_search_api_key
        self.google_engine_id = settings.google_search_engine_id
        self.use_google_api = bool(self.google_api_key and self.google_engine_id)
        
        # Headers để giả lập browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        logger.info(f"Web search service initialized. Using Google API: {self.use_google_api}")

    async def search(self, query: str, num_results: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm thông tin trên web
        
        Args:
            query: Từ khóa tìm kiếm
            num_results: Số lượng kết quả mong muốn
            
        Returns:
            Dict chứa kết quả tìm kiếm và tóm tắt
        """
        try:
            if self.use_google_api:
                # Sử dụng Google Custom Search API
                results = await self._search_with_google_api(query, num_results)
            else:
                # Fallback: scraping Google search results
                results = await self._search_with_scraping(query, num_results)
            
            if not results:
                return {"results": [], "summary": "Không tìm thấy kết quả nào."}
            
            # Lấy nội dung chi tiết từ các trang web
            detailed_results = await self._fetch_page_contents(results[:num_results])
            
            # Tạo tóm tắt từ các kết quả
            summary = await self._create_summary(detailed_results, query)
            
            return {
                "results": detailed_results,
                "summary": summary,
                "total_found": len(detailed_results)
            }
            
        except Exception as e:
            logger.error(f"Error in web search: {str(e)}")
            return {"results": [], "summary": "Có lỗi xảy ra khi tìm kiếm trên web."}

    async def _search_with_google_api(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Tìm kiếm bằng Google Custom Search API"""
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': self.google_api_key,
                'cx': self.google_engine_id,
                'q': query,
                'num': min(num_results, 10),  # Google API giới hạn 10 kết quả/request
                'hl': 'vi',  # Ngôn ngữ interface
                'lr': 'lang_vi',  # Ưu tiên kết quả tiếng Việt
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, timeout=10.0)
                response.raise_for_status()
                
                data = response.json()
                
                results = []
                for item in data.get('items', []):
                    results.append({
                        'title': item.get('title', ''),
                        'url': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'source': 'Google API'
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"Google API search failed: {str(e)}")
            return []

    async def _search_with_scraping(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Tìm kiếm bằng cách scraping Google search results"""
        try:
            # Tạo URL tìm kiếm Google
            search_url = f"https://www.google.com/search?q={quote_plus(query)}&hl=vi&num={num_results}"
            
            async with httpx.AsyncClient(headers=self.headers) as client:
                response = await client.get(search_url, timeout=10.0)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.text, 'html.parser')
                
                results = []
                
                # Tìm các kết quả tìm kiếm
                search_results = soup.find_all('div', class_='g')
                
                for result in search_results[:num_results]:
                    try:
                        # Lấy title và URL
                        title_elem = result.find('h3')
                        link_elem = result.find('a')
                        snippet_elem = result.find('span', class_=['aCOpRe', 'st'])
                        
                        if title_elem and link_elem:
                            title = title_elem.get_text(strip=True)
                            url = link_elem.get('href', '')
                            snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                            
                            # Làm sạch URL
                            if url.startswith('/url?q='):
                                url = url.split('/url?q=')[1].split('&')[0]
                            
                            if url and not url.startswith('http'):
                                continue
                            
                            results.append({
                                'title': title,
                                'url': url,
                                'snippet': snippet,
                                'source': 'Google Scraping'
                            })
                            
                    except Exception as e:
                        logger.warning(f"Error parsing search result: {str(e)}")
                        continue
                
                return results
                
        except Exception as e:
            logger.error(f"Google scraping search failed: {str(e)}")
            return []

    async def _fetch_page_contents(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Lấy nội dung chi tiết từ các trang web"""
        detailed_results = []
        
        for result in results:
            try:
                content = await self._fetch_single_page(result['url'])
                
                detailed_result = result.copy()
                detailed_result['content'] = content
                detailed_result['content_length'] = len(content) if content else 0
                
                detailed_results.append(detailed_result)
                
                # Delay nhỏ để tránh bị block
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.warning(f"Error fetching content from {result['url']}: {str(e)}")
                # Vẫn thêm kết quả nhưng không có content
                detailed_result = result.copy()
                detailed_result['content'] = result.get('snippet', '')
                detailed_result['content_length'] = len(detailed_result['content'])
                detailed_results.append(detailed_result)
        
        return detailed_results

    async def _fetch_single_page(self, url: str) -> str:
        """Lấy nội dung từ một trang web"""
        try:
            async with httpx.AsyncClient(headers=self.headers) as client:
                response = await client.get(url, timeout=10.0, follow_redirects=True)
                response.raise_for_status()
                
                # Parse HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Xóa các thẻ không cần thiết
                for tag in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
                    tag.decompose()
                
                # Lấy text từ các thẻ chính
                content_tags = soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'div'])
                
                content_parts = []
                for tag in content_tags:
                    text = tag.get_text(strip=True)
                    if text and len(text) > 20:  # Chỉ lấy text có ý nghĩa
                        content_parts.append(text)
                
                content = '\n'.join(content_parts)
                
                # Giới hạn độ dài nội dung
                if len(content) > 5000:
                    content = content[:5000] + "..."
                
                return content
                
        except Exception as e:
            logger.error(f"Error fetching page {url}: {str(e)}")
            return ""

    async def _create_summary(self, results: List[Dict[str, Any]], query: str) -> str:
        """Tạo tóm tắt từ các kết quả tìm kiếm"""
        try:
            if not results:
                return "Không có thông tin để tóm tắt."
            
            # Tổng hợp nội dung từ các nguồn
            all_content = []
            sources = []
            
            for result in results:
                if result.get('content'):
                    all_content.append(f"Từ {result['title']}: {result['content'][:1000]}")
                    sources.append(result['title'])
                elif result.get('snippet'):
                    all_content.append(f"Từ {result['title']}: {result['snippet']}")
                    sources.append(result['title'])
            
            if not all_content:
                return "Không thể tạo tóm tắt từ các kết quả tìm kiếm."
            
            # Tạo tóm tắt đơn giản
            combined_content = '\n\n'.join(all_content[:3])  # Chỉ lấy 3 nguồn đầu
            
            summary_parts = [
                f"Dựa trên kết quả tìm kiếm cho '{query}', đây là thông tin tổng hợp:",
                "",
                combined_content,
                "",
                f"Nguồn: {', '.join(sources[:3])}"
            ]
            
            return '\n'.join(summary_parts)
            
        except Exception as e:
            logger.error(f"Error creating summary: {str(e)}")
            return "Có lỗi khi tạo tóm tắt thông tin."

    async def search_specific_site(self, query: str, site: str) -> Dict[str, Any]:
        """Tìm kiếm trong một trang web cụ thể"""
        try:
            site_query = f"site:{site} {query}"
            return await self.search(site_query, num_results=3)
            
        except Exception as e:
            logger.error(f"Error searching specific site {site}: {str(e)}")
            return {"results": [], "summary": "Có lỗi khi tìm kiếm trong trang web cụ thể."}
