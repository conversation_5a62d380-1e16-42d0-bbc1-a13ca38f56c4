<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Agent - <PERSON><PERSON> tích dữ liệu đa định dạng</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-robot"></i> AI Agent</h4>
                    <p class="text-muted"><PERSON>ân tích dữ liệu đa định dạng</p>
                </div>
                
                <!-- File Upload Section -->
                <div class="upload-section">
                    <h6><i class="fas fa-upload"></i> Upload Files</h6>
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Kéo thả file hoặc click để chọn</p>
                        <small>Hỗ trợ: PDF, Word, Excel, Ảnh, Audio, Video</small>
                        <input type="file" id="fileInput" multiple accept=".pdf,.docx,.doc,.xlsx,.xls,.jpg,.jpeg,.png,.gif,.mp3,.wav,.mp4,.avi,.mov" style="display: none;">
                    </div>
                    <div id="uploadProgress" class="upload-progress" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small id="uploadStatus">Đang upload...</small>
                    </div>
                </div>
                
                <!-- Documents List -->
                <div class="documents-section">
                    <h6><i class="fas fa-file-alt"></i> Documents</h6>
                    <div id="documentsList" class="documents-list">
                        <div class="text-muted text-center">
                            <small>Chưa có document nào</small>
                        </div>
                    </div>
                </div>
                
                <!-- Statistics -->
                <div class="stats-section">
                    <h6><i class="fas fa-chart-bar"></i> Thống kê</h6>
                    <div id="statsInfo" class="stats-info">
                        <div class="stat-item">
                            <span class="stat-label">Documents:</span>
                            <span class="stat-value" id="totalDocs">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Chunks:</span>
                            <span class="stat-value" id="totalChunks">0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Chat Area -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Chat Header -->
                <div class="chat-header">
                    <h5><i class="fas fa-comments"></i> Chat với AI Agent</h5>
                    <div class="chat-controls">
                        <button class="btn btn-outline-secondary btn-sm" id="clearChat">
                            <i class="fas fa-trash"></i> Xóa chat
                        </button>
                        <button class="btn btn-outline-info btn-sm" id="refreshDocs">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>
                
                <!-- Chat Messages -->
                <div class="chat-container" id="chatContainer">
                    <div class="welcome-message">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                                <h5>Chào mừng đến với AI Agent!</h5>
                                <p class="text-muted">
                                    Tôi có thể giúp bạn phân tích các loại file: PDF, Word, Excel, Ảnh, Audio, Video.<br>
                                    Hãy upload file và đặt câu hỏi về nội dung của chúng!
                                </p>
                                <div class="example-questions">
                                    <h6>Ví dụ câu hỏi:</h6>
                                    <div class="example-question" data-question="Tóm tắt nội dung chính của document">
                                        "Tóm tắt nội dung chính của document"
                                    </div>
                                    <div class="example-question" data-question="Có những thông tin gì quan trọng trong file?">
                                        "Có những thông tin gì quan trọng trong file?"
                                    </div>
                                    <div class="example-question" data-question="Phân tích dữ liệu trong bảng Excel">
                                        "Phân tích dữ liệu trong bảng Excel"
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Chat Input -->
                <div class="chat-input-container">
                    <div class="input-group">
                        <input type="text" class="form-control" id="messageInput" placeholder="Nhập câu hỏi của bạn..." autocomplete="off">
                        <button class="btn btn-primary" type="button" id="sendButton">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="input-help">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb"></i> 
                            Tip: AI sẽ ưu tiên tìm kiếm trong documents đã upload, sau đó mới search web
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">AI đang suy nghĩ...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- File Preview Modal -->
    <div class="modal fade" id="filePreviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filePreviewTitle">Chi tiết Document</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="filePreviewBody">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" id="deleteFileBtn">
                        <i class="fas fa-trash"></i> Xóa file
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notificationToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">Thông báo</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- Message will be set here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
