"""
Logging configuration for FastAPI Gemini AI Agent
"""

import logging
import logging.config
import os
from pathlib import Path

# T<PERSON><PERSON> thư mục logs nếu chưa có
logs_dir = Path("logs")
logs_dir.mkdir(exist_ok=True)

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "json": {
            "format": '{"timestamp": "%(asctime)s", "name": "%(name)s", "level": "%(levelname)s", "message": "%(message)s"}',
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "default",
            "stream": "ext://sys.stdout"
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "DEBUG",
            "formatter": "detailed",
            "filename": "logs/ai_agent.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "encoding": "utf8"
        },
        "error_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "ERROR",
            "formatter": "detailed",
            "filename": "logs/ai_agent_errors.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "encoding": "utf8"
        },
        "access_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "INFO",
            "formatter": "default",
            "filename": "logs/access.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "encoding": "utf8"
        }
    },
    "loggers": {
        "": {  # Root logger
            "level": "INFO",
            "handlers": ["console", "file", "error_file"]
        },
        "app": {
            "level": "DEBUG",
            "handlers": ["console", "file", "error_file"],
            "propagate": False
        },
        "uvicorn.access": {
            "level": "INFO",
            "handlers": ["access_file"],
            "propagate": False
        },
        "uvicorn.error": {
            "level": "INFO",
            "handlers": ["console", "error_file"],
            "propagate": False
        },
        "fastapi": {
            "level": "INFO",
            "handlers": ["console", "file"],
            "propagate": False
        },
        "httpx": {
            "level": "WARNING",
            "handlers": ["file"],
            "propagate": False
        },
        "chromadb": {
            "level": "WARNING",
            "handlers": ["file"],
            "propagate": False
        }
    }
}


def setup_logging(debug: bool = False):
    """
    Setup logging configuration
    
    Args:
        debug: If True, set more verbose logging
    """
    # Adjust log levels based on debug mode
    if debug:
        LOGGING_CONFIG["loggers"][""]["level"] = "DEBUG"
        LOGGING_CONFIG["loggers"]["app"]["level"] = "DEBUG"
        LOGGING_CONFIG["handlers"]["console"]["level"] = "DEBUG"
    
    # Apply logging configuration
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # Get logger and log startup message
    logger = logging.getLogger("app.core.logging")
    logger.info("Logging configuration initialized")
    logger.info(f"Debug mode: {debug}")
    logger.info(f"Log files location: {logs_dir.absolute()}")


def get_logger(name: str) -> logging.Logger:
    """
    Get logger with specified name
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


# Custom log filters
class HealthCheckFilter(logging.Filter):
    """Filter out health check requests from access logs"""
    
    def filter(self, record):
        return "/health" not in record.getMessage()


class SensitiveDataFilter(logging.Filter):
    """Filter out sensitive data from logs"""
    
    SENSITIVE_PATTERNS = [
        "api_key",
        "password",
        "token",
        "secret",
        "authorization"
    ]
    
    def filter(self, record):
        message = record.getMessage().lower()
        for pattern in self.SENSITIVE_PATTERNS:
            if pattern in message:
                # Replace sensitive data with asterisks
                record.msg = record.msg.replace(
                    record.msg[record.msg.lower().find(pattern):],
                    f"{pattern}=***REDACTED***"
                )
        return True


# Performance logging decorator
def log_performance(func):
    """Decorator to log function performance"""
    import time
    import functools
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        logger = get_logger(f"app.performance.{func.__module__}.{func.__name__}")
        start_time = time.time()
        
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"Execution time: {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Failed after {execution_time:.3f}s: {str(e)}")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        logger = get_logger(f"app.performance.{func.__module__}.{func.__name__}")
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"Execution time: {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Failed after {execution_time:.3f}s: {str(e)}")
            raise
    
    # Return appropriate wrapper based on function type
    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


# Context manager for logging operations
class LogOperation:
    """Context manager for logging operations with timing"""
    
    def __init__(self, operation_name: str, logger_name: str = "app"):
        self.operation_name = operation_name
        self.logger = get_logger(logger_name)
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.info(f"Starting operation: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        execution_time = time.time() - self.start_time
        
        if exc_type is None:
            self.logger.info(f"Completed operation: {self.operation_name} in {execution_time:.3f}s")
        else:
            self.logger.error(f"Failed operation: {self.operation_name} after {execution_time:.3f}s - {exc_val}")
        
        return False  # Don't suppress exceptions
