# FastAPI Gemini AI Agent

AI Agent chuyên phân tích dữ liệu đa định dạng với FastAPI và Gemini AI. Hỗ trợ xử lý các loại file: PDF, Word, Excel, Ảnh, Audio, Video với giao diện chatbot thân thiện.

## ✨ Tính năng chính

- 🤖 **AI Chatbot thông minh**: Sử dụng Gemini AI để trả lời câu hỏi
- 📄 **Xử lý đa định dạng**: PDF, Word (.docx), Excel (.xlsx), Ảnh (jpg, png), Audio (mp3, wav), Video (mp4, avi, mov)
- 🔍 **Tìm kiếm thông minh**: Ưu tiên tìm trong documents đã upload, sau đó search web
- 💾 **Lưu trữ vector**: Sử dụng ChromaDB để lưu trữ và tìm kiếm nội dung
- 🎨 **Giao diện đẹp**: Web interface responsive với Bootstrap
- ⚡ **<PERSON><PERSON> lý bất đồng bộ**: FastAPI với async/await

## 🚀 Cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd FastAPI_Gemini_AI
```

### 2. Tạo virtual environment
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows
```

### 3. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 4. Cấu hình môi trường
Tạo file `.env` từ `.env.example`:
```bash
cp .env.example .env
```

Chỉnh sửa file `.env` và thêm API key:
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

### 5. Chạy ứng dụng
```bash
python run.py
```

Hoặc sử dụng uvicorn trực tiếp:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 🌐 Sử dụng

1. Mở trình duyệt và truy cập: `http://localhost:8000`
2. Upload các file cần phân tích (kéo thả hoặc click để chọn)
3. Đặt câu hỏi về nội dung file trong chatbox
4. AI sẽ phân tích và trả lời dựa trên nội dung file

### Ví dụ câu hỏi:
- "Tóm tắt nội dung chính của document"
- "Có những thông tin gì quan trọng trong file?"
- "Phân tích dữ liệu trong bảng Excel"
- "Nội dung audio nói về gì?"
- "Mô tả hình ảnh này"

## 📁 Cấu trúc dự án

```
FastAPI_Gemini_AI/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI app chính
│   ├── core/
│   │   ├── __init__.py
│   │   └── config.py          # Cấu hình ứng dụng
│   ├── api/
│   │   ├── __init__.py
│   │   ├── chat.py            # API chat
│   │   ├── upload.py          # API upload file
│   │   └── documents.py       # API quản lý documents
│   ├── services/
│   │   ├── __init__.py
│   │   ├── gemini_service.py      # Service Gemini AI
│   │   ├── document_service.py    # Service quản lý documents
│   │   ├── file_processor_service.py  # Service xử lý file
│   │   └── web_search_service.py  # Service tìm kiếm web
│   └── processors/
│       ├── __init__.py
│       ├── pdf_processor.py       # Xử lý PDF
│       ├── word_processor.py      # Xử lý Word
│       ├── excel_processor.py     # Xử lý Excel
│       ├── image_processor.py     # Xử lý ảnh
│       ├── audio_processor.py     # Xử lý audio
│       └── video_processor.py     # Xử lý video
├── static/
│   ├── css/
│   │   └── style.css          # CSS cho giao diện
│   └── js/
│       └── app.js             # JavaScript cho frontend
├── templates/
│   └── index.html             # Template HTML chính
├── uploads/                   # Thư mục lưu file upload
├── chroma_db/                 # Thư mục ChromaDB
├── tests/                     # Unit tests
├── requirements.txt           # Python dependencies
├── .env.example              # File cấu hình mẫu
├── .gitignore
├── run.py                    # Script chạy ứng dụng
└── README.md
```

## 🔧 API Endpoints

### Chat API
- `POST /api/chat/` - Gửi tin nhắn chat
- `GET /api/chat/history/{session_id}` - Lấy lịch sử chat

### Upload API
- `POST /api/upload/file` - Upload một file
- `POST /api/upload/files` - Upload nhiều file
- `DELETE /api/upload/file/{file_id}` - Xóa file

### Documents API
- `GET /api/documents/` - Lấy danh sách documents
- `GET /api/documents/{file_id}` - Lấy thông tin chi tiết document
- `POST /api/documents/search` - Tìm kiếm trong documents
- `GET /api/documents/{file_id}/content` - Lấy nội dung document
- `DELETE /api/documents/{file_id}` - Xóa document

## 🛠️ Công nghệ sử dụng

- **Backend**: FastAPI, Python 3.8+
- **AI**: Google Gemini AI
- **Vector Database**: ChromaDB
- **File Processing**: 
  - PDF: PyPDF2, pdfplumber
  - Word: python-docx
  - Excel: openpyxl, pandas
  - Image: Pillow, OpenCV
  - Audio: pydub, speech-recognition, whisper
  - Video: moviepy, OpenCV
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Web Search**: requests, BeautifulSoup4

## ⚙️ Cấu hình

Các cấu hình chính trong file `.env`:

```env
# Gemini AI
GEMINI_API_KEY=your_api_key

# Database
DATABASE_URL=sqlite:///./ai_agent.db

# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_DIRECTORY=uploads

# Vector Database
CHROMA_PERSIST_DIRECTORY=./chroma_db

# Web Search (Optional)
GOOGLE_SEARCH_API_KEY=your_google_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
```

## 🧪 Testing

Chạy tests:
```bash
pytest tests/
```

## 📝 Ghi chú

- Lần đầu chạy có thể mất thời gian để tải các model AI (Whisper, Sentence Transformers)
- File upload tối đa 50MB (có thể thay đổi trong config)
- Hỗ trợ tiếng Việt và tiếng Anh
- Cần kết nối internet để sử dụng Gemini AI và web search

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Liên hệ

- Email: <EMAIL>
- GitHub: [your-github-username](https://github.com/your-github-username)

---

**Lưu ý**: Đây là phiên bản demo. Trong môi trường production, cần thêm các tính năng bảo mật, authentication, rate limiting, và monitoring.
