"""
Factory để tạo document service phù hợp dựa trên dependencies có sẵn
"""

import logging

logger = logging.getLogger(__name__)


def create_document_service():
    """Tạo document service phù hợp"""
    try:
        # Try to import and use advanced document service
        import chromadb
        from sentence_transformers import SentenceTransformer
        from app.services.document_service import DocumentService
        
        logger.info("Using advanced DocumentService with ChromaDB")
        return DocumentService()
        
    except ImportError as e:
        # Fallback to simple document service
        logger.warning(f"Advanced dependencies not available ({e}), using SimpleDocumentService")
        from app.services.simple_document_service import SimpleDocumentService
        return SimpleDocumentService()


def create_gemini_service():
    """Tạo Gemini service nếu có thể"""
    try:
        from app.services.gemini_service import GeminiService
        return GeminiService()
    except ImportError as e:
        logger.error(f"Gemini service not available: {e}")
        return None


def create_web_search_service():
    """Tạo web search service"""
    try:
        from app.services.web_search_service import WebSearchService
        return WebSearchService()
    except ImportError as e:
        logger.warning(f"Web search service not available: {e}")
        return None
