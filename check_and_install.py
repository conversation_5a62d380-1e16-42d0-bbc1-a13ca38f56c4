#!/usr/bin/env python3
"""
Script kiểm tra và cài đặt dependencies cho FastAPI Gemini AI Agent
"""

import subprocess
import sys
import os

def check_python_version():
    """Kiểm tra phiên bản Python"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("✅ Python version is supported")
        return True
    else:
        print("❌ Python 3.8+ is required")
        return False

def run_command(command, description):
    """Chạy command và trả về kết quả"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"Error: {e.stderr}")
        return False

def check_package(package_name, import_name=None):
    """Kiểm tra xem package có được cài đặt không"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} is installed")
        return True
    except ImportError:
        print(f"❌ {package_name} is not installed")
        return False

def install_package(package_name):
    """Cài đặt package"""
    return run_command(f"pip install {package_name}", f"Installing {package_name}")

def main():
    """Main function"""
    print("🚀 FastAPI Gemini AI Agent - Dependency Checker & Installer")
    print("="*70)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Upgrade pip
    print("\n📦 Upgrading pip...")
    run_command("python -m pip install --upgrade pip", "Upgrading pip")
    
    # Core packages to check and install
    core_packages = [
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("python-multipart", "multipart"),
        ("jinja2", "jinja2"),
        ("aiofiles", "aiofiles"),
        ("pydantic-settings", "pydantic_settings"),
        ("google-generativeai", "google.generativeai"),
        ("PyPDF2", "PyPDF2"),
        ("python-docx", "docx"),
        ("openpyxl", "openpyxl"),
        ("pandas", "pandas"),
        ("Pillow", "PIL"),
        ("requests", "requests"),
        ("beautifulsoup4", "bs4"),
        ("python-dotenv", "dotenv"),
        ("httpx", "httpx")
    ]
    
    print("\n📋 Checking core dependencies...")
    missing_packages = []
    
    for package_name, import_name in core_packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    # Install missing packages
    if missing_packages:
        print(f"\n🔧 Installing {len(missing_packages)} missing packages...")
        failed_installs = []
        
        for package in missing_packages:
            if not install_package(package):
                failed_installs.append(package)
        
        if failed_installs:
            print(f"\n❌ Failed to install: {', '.join(failed_installs)}")
            print("\n💡 Try installing manually:")
            for package in failed_installs:
                print(f"   pip install {package}")
            return False
    else:
        print("\n✅ All core dependencies are installed!")
    
    # Check optional packages
    print("\n📋 Checking optional dependencies...")
    optional_packages = [
        ("pdfplumber", "pdfplumber", "Advanced PDF processing"),
        ("opencv-python-headless", "cv2", "Image processing"),
        ("pydub", "pydub", "Audio processing"),
        ("SpeechRecognition", "speech_recognition", "Speech recognition"),
        ("moviepy", "moviepy.editor", "Video processing"),
        ("chromadb", "chromadb", "Vector database"),
        ("sentence-transformers", "sentence_transformers", "Text embeddings")
    ]
    
    missing_optional = []
    for package_name, import_name, description in optional_packages:
        if check_package(package_name, import_name):
            print(f"  📦 {description}: Available")
        else:
            print(f"  ⚠️  {description}: Not available")
            missing_optional.append((package_name, description))
    
    # Ask about installing optional packages
    if missing_optional:
        print(f"\n🤔 Found {len(missing_optional)} optional packages not installed.")
        print("These provide advanced features but are not required for basic functionality.")
        
        response = input("\nInstall optional packages? (y/n): ")
        if response.lower() == 'y':
            print("\n🔧 Installing optional packages...")
            for package_name, description in missing_optional:
                print(f"\nInstalling {package_name} ({description})...")
                install_package(package_name)
    
    # Check environment file
    print("\n📄 Checking environment configuration...")
    if os.path.exists('.env'):
        print("✅ .env file exists")
        
        # Check for API key
        with open('.env', 'r') as f:
            content = f.read()
            if 'GEMINI_API_KEY=' in content and 'your_gemini_api_key_here' not in content:
                print("✅ GEMINI_API_KEY appears to be configured")
            else:
                print("⚠️  GEMINI_API_KEY needs to be configured in .env file")
    else:
        print("⚠️  .env file not found")
        if os.path.exists('.env.example'):
            print("📋 Creating .env from .env.example...")
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ .env file created. Please edit it and add your GEMINI_API_KEY")
        else:
            print("❌ .env.example not found")
    
    # Test basic imports
    print("\n🧪 Testing basic functionality...")
    test_imports = [
        "import fastapi",
        "import uvicorn",
        "from app.main import app"
    ]
    
    all_tests_passed = True
    for test_import in test_imports:
        try:
            exec(test_import)
            print(f"✅ {test_import}")
        except Exception as e:
            print(f"❌ {test_import} - {e}")
            all_tests_passed = False
    
    # Final summary
    print("\n" + "="*70)
    if all_tests_passed:
        print("🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit .env file and add your GEMINI_API_KEY")
        print("2. Run the application: python run.py")
        print("3. Open browser: http://localhost:8000")
        
        print("\n💡 Features available:")
        print("✅ Basic file upload and processing")
        print("✅ PDF, Word, Excel, Image support")
        if check_package("chromadb", "chromadb"):
            print("✅ Advanced document search")
        else:
            print("⚠️  Simple document search (install chromadb for advanced features)")
        
        if check_package("pydub", "pydub"):
            print("✅ Audio processing")
        else:
            print("⚠️  Basic audio info only (install pydub for transcription)")
            
        if check_package("moviepy", "moviepy.editor"):
            print("✅ Video processing")
        else:
            print("⚠️  Basic video info only (install moviepy for analysis)")
    else:
        print("❌ Setup completed with issues")
        print("Please resolve the errors above before running the application")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
