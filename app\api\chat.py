from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import logging

from app.services.document_factory import (
    create_document_service,
    create_gemini_service,
    create_web_search_service
)

router = APIRouter()

# Initialize services with fallbacks
document_service = create_document_service()
gemini_service = create_gemini_service()
web_search_service = create_web_search_service()

logger = logging.getLogger(__name__)


class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None


class ChatResponse(BaseModel):
    response: str
    sources: List[str] = []
    source_type: str  # "documents", "web", "gemini"


@router.post("/", response_model=ChatResponse)
async def chat(chat_message: ChatMessage):
    """
    Xử lý tin nhắn chat từ người dùng
    Ưu tiên tìm kiếm trong documents, sau đó web search, cuối cùng là <PERSON>
    """
    try:
        user_message = chat_message.message
        session_id = chat_message.session_id or "default"
        
        # Bước 1: <PERSON><PERSON><PERSON> kiế<PERSON> trong documents đã upload
        document_results = await document_service.search_documents(user_message)

        if document_results and document_results.get("relevant_content"):
            # Có thông tin từ documents
            context = document_results["relevant_content"]
            sources = document_results.get("sources", [])

            # Sử dụng Gemini để trả lời dựa trên context từ documents (nếu có)
            if gemini_service:
                prompt = f"""
                Dựa trên thông tin sau từ các tài liệu đã cung cấp, hãy trả lời câu hỏi của người dùng:

                Thông tin từ tài liệu:
                {context}

                Câu hỏi của người dùng: {user_message}

                Hãy trả lời một cách chính xác và chi tiết dựa trên thông tin có sẵn.
                """

                response = await gemini_service.generate_response(prompt)
            else:
                # Fallback response without Gemini
                response = f"Dựa trên tài liệu đã upload, tôi tìm thấy thông tin sau:\n\n{context}"

            return ChatResponse(
                response=response,
                sources=sources,
                source_type="documents"
            )
        
        # Bước 2: Tìm kiếm trên web nếu không tìm thấy trong documents
        if web_search_service:
            web_results = await web_search_service.search(user_message)

            if web_results and web_results.get("results"):
                # Có thông tin từ web
                web_context = web_results["summary"]
                sources = [result["url"] for result in web_results["results"][:3]]

                if gemini_service:
                    prompt = f"""
                    Dựa trên thông tin tìm kiếm từ web sau, hãy trả lời câu hỏi của người dùng:

                    Thông tin từ web:
                    {web_context}

                    Câu hỏi của người dùng: {user_message}

                    Hãy tổng hợp và trả lời một cách chính xác.
                    """

                    response = await gemini_service.generate_response(prompt)
                else:
                    response = f"Thông tin từ web:\n\n{web_context}"

                return ChatResponse(
                    response=response,
                    sources=sources,
                    source_type="web"
                )

        # Bước 3: Sử dụng Gemini trực tiếp nếu không tìm thấy thông tin
        if gemini_service:
            response = await gemini_service.generate_response(user_message)

            return ChatResponse(
                response=response,
                sources=[],
                source_type="gemini"
            )
        else:
            # Fallback response when no services available
            return ChatResponse(
                response="Xin lỗi, tôi không thể trả lời câu hỏi này vì các dịch vụ AI chưa được cấu hình đầy đủ. Vui lòng kiểm tra cấu hình GEMINI_API_KEY.",
                sources=[],
                source_type="error"
            )
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi xử lý tin nhắn: {str(e)}")


@router.get("/history/{session_id}")
async def get_chat_history(session_id: str):
    """Lấy lịch sử chat của session"""
    # TODO: Implement chat history storage and retrieval
    return {"session_id": session_id, "messages": []}
