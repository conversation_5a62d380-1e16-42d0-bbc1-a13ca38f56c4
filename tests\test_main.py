import pytest
from fastapi.testclient import TestClient
import os
import tempfile
from unittest.mock import patch, MagicMock

# Import the app
from app.main import app

client = TestClient(app)


def test_health_check():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert data["status"] == "healthy"


def test_root_endpoint():
    """Test root endpoint returns HTML"""
    response = client.get("/")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]


class TestChatAPI:
    """Test chat API endpoints"""
    
    @patch('app.services.gemini_service.GeminiService.generate_response')
    @patch('app.services.document_service.DocumentService.search_documents')
    @patch('app.services.web_search_service.WebSearchService.search')
    def test_chat_with_documents(self, mock_web_search, mock_doc_search, mock_gemini):
        """Test chat when documents are found"""
        # Mock document search returning results
        mock_doc_search.return_value = {
            "relevant_content": "Test content from documents",
            "sources": ["test.pdf"],
            "results": [{"content": "test", "filename": "test.pdf"}]
        }
        
        # Mock Gemini response
        mock_gemini.return_value = "This is a test response based on documents"
        
        response = client.post("/api/chat/", json={
            "message": "What is in the document?",
            "session_id": "test_session"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "response" in data
        assert data["source_type"] == "documents"
        assert "test.pdf" in data["sources"]
    
    @patch('app.services.gemini_service.GeminiService.generate_response')
    @patch('app.services.document_service.DocumentService.search_documents')
    @patch('app.services.web_search_service.WebSearchService.search')
    def test_chat_with_web_search(self, mock_web_search, mock_doc_search, mock_gemini):
        """Test chat when no documents found, falls back to web search"""
        # Mock document search returning no results
        mock_doc_search.return_value = {
            "relevant_content": "",
            "sources": [],
            "results": []
        }
        
        # Mock web search returning results
        mock_web_search.return_value = {
            "results": [{"url": "http://example.com", "title": "Test"}],
            "summary": "Web search results summary"
        }
        
        # Mock Gemini response
        mock_gemini.return_value = "This is a response based on web search"
        
        response = client.post("/api/chat/", json={
            "message": "What is the weather today?",
            "session_id": "test_session"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "response" in data
        assert data["source_type"] == "web"
    
    @patch('app.services.gemini_service.GeminiService.generate_response')
    @patch('app.services.document_service.DocumentService.search_documents')
    @patch('app.services.web_search_service.WebSearchService.search')
    def test_chat_gemini_only(self, mock_web_search, mock_doc_search, mock_gemini):
        """Test chat when no documents or web results, uses Gemini only"""
        # Mock no results from documents and web
        mock_doc_search.return_value = {
            "relevant_content": "",
            "sources": [],
            "results": []
        }
        
        mock_web_search.return_value = {
            "results": [],
            "summary": ""
        }
        
        # Mock Gemini response
        mock_gemini.return_value = "This is a direct Gemini response"
        
        response = client.post("/api/chat/", json={
            "message": "Hello, how are you?",
            "session_id": "test_session"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "response" in data
        assert data["source_type"] == "gemini"


class TestUploadAPI:
    """Test file upload API endpoints"""
    
    def test_upload_invalid_file_type(self):
        """Test uploading invalid file type"""
        # Create a temporary file with invalid extension
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp_file:
            tmp_file.write(b"test content")
            tmp_file_path = tmp_file.name
        
        try:
            with open(tmp_file_path, "rb") as f:
                response = client.post(
                    "/api/upload/file",
                    files={"file": ("test.txt", f, "text/plain")}
                )
            
            assert response.status_code == 400
            data = response.json()
            assert "không được hỗ trợ" in data["detail"]
        
        finally:
            os.unlink(tmp_file_path)
    
    @patch('app.services.file_processor_service.FileProcessorService.process_file')
    @patch('app.services.document_service.DocumentService.add_document')
    def test_upload_valid_file(self, mock_add_doc, mock_process_file):
        """Test uploading valid file"""
        # Mock file processing
        mock_process_file.return_value = "Extracted content from file"
        mock_add_doc.return_value = {
            "file_id": "test_id",
            "filename": "test.pdf",
            "status": "success"
        }
        
        # Create a temporary PDF-like file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_file.write(b"fake pdf content")
            tmp_file_path = tmp_file.name
        
        try:
            with open(tmp_file_path, "rb") as f:
                response = client.post(
                    "/api/upload/file",
                    files={"file": ("test.pdf", f, "application/pdf")}
                )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert "file_id" in data
        
        finally:
            os.unlink(tmp_file_path)


class TestDocumentsAPI:
    """Test documents API endpoints"""
    
    @patch('app.services.document_service.DocumentService.get_all_documents')
    def test_list_documents(self, mock_get_docs):
        """Test listing all documents"""
        mock_get_docs.return_value = [
            {
                "file_id": "test1",
                "filename": "test1.pdf",
                "file_type": ".pdf",
                "upload_time": "2024-01-01T00:00:00",
                "chunk_count": 5
            }
        ]
        
        response = client.get("/api/documents/")
        assert response.status_code == 200
        data = response.json()
        assert "documents" in data
        assert len(data["documents"]) == 1
    
    @patch('app.services.document_service.DocumentService.get_document')
    def test_get_document_details(self, mock_get_doc):
        """Test getting document details"""
        mock_get_doc.return_value = {
            "file_id": "test1",
            "filename": "test1.pdf",
            "file_type": ".pdf",
            "short_summary": "Test summary",
            "keywords": ["test", "document"]
        }
        
        response = client.get("/api/documents/test1")
        assert response.status_code == 200
        data = response.json()
        assert data["file_id"] == "test1"
        assert data["filename"] == "test1.pdf"
    
    @patch('app.services.document_service.DocumentService.get_document')
    def test_get_nonexistent_document(self, mock_get_doc):
        """Test getting non-existent document"""
        mock_get_doc.return_value = None
        
        response = client.get("/api/documents/nonexistent")
        assert response.status_code == 404
    
    @patch('app.services.document_service.DocumentService.search_documents')
    def test_search_documents(self, mock_search):
        """Test searching documents"""
        mock_search.return_value = {
            "relevant_content": "Found content",
            "sources": ["test.pdf"],
            "results": [{"content": "test", "filename": "test.pdf"}],
            "total_found": 1
        }
        
        response = client.post("/api/documents/search?query=test&limit=5")
        assert response.status_code == 200
        data = response.json()
        assert "relevant_content" in data
        assert data["total_found"] == 1


if __name__ == "__main__":
    pytest.main([__file__])
