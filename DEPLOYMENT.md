# Deployment Guide - FastAPI Gemini AI Agent

Hướng dẫn deploy ứng dụng AI Agent l<PERSON><PERSON> c<PERSON><PERSON> môi trường khác nhau.

## 🚀 Quick Start với Docker

### 1. Chu<PERSON><PERSON> bị
```bash
# Clone repository
git clone <repository-url>
cd FastAPI_Gemini_AI

# Tạo file .env
cp .env.example .env
# Chỉnh sửa .env và thêm GEMINI_API_KEY
```

### 2. Build và chạy với Docker Compose
```bash
# Build và start tất cả services
docker-compose up -d

# Xem logs
docker-compose logs -f ai-agent

# Stop services
docker-compose down
```

### 3. Chỉ chạy AI Agent
```bash
# Build image
docker build -t fastapi-gemini-ai .

# Run container
docker run -d \
  --name ai-agent \
  -p 8000:8000 \
  -e GEMINI_API_KEY=your_api_key \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/chroma_db:/app/chroma_db \
  fastapi-gemini-ai
```

## 🌐 Production Deployment

### 1. VPS/Server Deployment

#### Chuẩn bị server
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### Deploy ứng dụng
```bash
# Clone code
git clone <repository-url>
cd FastAPI_Gemini_AI

# Setup environment
cp .env.example .env
nano .env  # Chỉnh sửa cấu hình

# Deploy
docker-compose -f docker-compose.yml up -d

# Setup Nginx (optional)
sudo apt install nginx
sudo cp nginx.conf /etc/nginx/sites-available/ai-agent
sudo ln -s /etc/nginx/sites-available/ai-agent /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx
```

### 2. Cloud Deployment

#### AWS EC2
```bash
# Launch EC2 instance (Ubuntu 22.04)
# Security Group: Allow ports 22, 80, 443, 8000

# Connect to instance
ssh -i your-key.pem ubuntu@your-ec2-ip

# Install Docker và deploy như trên
```

#### Google Cloud Platform
```bash
# Create VM instance
gcloud compute instances create ai-agent-vm \
  --image-family=ubuntu-2204-lts \
  --image-project=ubuntu-os-cloud \
  --machine-type=e2-medium \
  --boot-disk-size=20GB

# SSH and deploy
gcloud compute ssh ai-agent-vm
```

#### DigitalOcean
```bash
# Create droplet với Docker pre-installed
# SSH và deploy như hướng dẫn VPS
```

### 3. Kubernetes Deployment

#### Tạo Kubernetes manifests
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ai-agent

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-agent-config
  namespace: ai-agent
data:
  DATABASE_URL: "sqlite:///./ai_agent.db"
  CHROMA_PERSIST_DIRECTORY: "./chroma_db"
  UPLOAD_DIRECTORY: "uploads"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: ai-agent-secrets
  namespace: ai-agent
type: Opaque
stringData:
  GEMINI_API_KEY: "your_gemini_api_key_here"

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-agent
  namespace: ai-agent
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai-agent
  template:
    metadata:
      labels:
        app: ai-agent
    spec:
      containers:
      - name: ai-agent
        image: fastapi-gemini-ai:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: ai-agent-config
        - secretRef:
            name: ai-agent-secrets
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
        - name: chroma-db
          mountPath: /app/chroma_db
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: uploads-pvc
      - name: chroma-db
        persistentVolumeClaim:
          claimName: chroma-db-pvc

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ai-agent-service
  namespace: ai-agent
spec:
  selector:
    app: ai-agent
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

#### Deploy lên Kubernetes
```bash
# Apply manifests
kubectl apply -f k8s/

# Check status
kubectl get pods -n ai-agent
kubectl get services -n ai-agent

# Get external IP
kubectl get service ai-agent-service -n ai-agent
```

## 🔧 Configuration

### Environment Variables
```env
# Required
GEMINI_API_KEY=your_gemini_api_key

# Optional
DATABASE_URL=sqlite:///./ai_agent.db
CHROMA_PERSIST_DIRECTORY=./chroma_db
UPLOAD_DIRECTORY=uploads
MAX_FILE_SIZE=52428800  # 50MB
DEBUG=False

# Web Search (Optional)
GOOGLE_SEARCH_API_KEY=your_google_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id

# Security
ALLOWED_ORIGINS=["*"]  # Restrict in production
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    client_max_body_size 50M;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        alias /path/to/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 📊 Monitoring

### Health Checks
```bash
# Application health
curl http://localhost:8000/health

# Docker health
docker ps
docker logs ai-agent

# System resources
docker stats ai-agent
```

### Logging
```bash
# View application logs
docker-compose logs -f ai-agent

# View specific log files
tail -f logs/ai_agent.log
tail -f logs/ai_agent_errors.log
```

## 🔒 Security

### Production Security Checklist
- [ ] Set strong passwords for databases
- [ ] Use HTTPS with SSL certificates
- [ ] Restrict CORS origins
- [ ] Enable rate limiting
- [ ] Set up firewall rules
- [ ] Regular security updates
- [ ] Monitor logs for suspicious activity
- [ ] Use secrets management for API keys

### SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Container won't start
```bash
# Check logs
docker logs ai-agent

# Check environment variables
docker exec ai-agent env

# Check file permissions
docker exec ai-agent ls -la /app
```

#### 2. File upload fails
```bash
# Check disk space
df -h

# Check upload directory permissions
ls -la uploads/

# Check file size limits
grep MAX_FILE_SIZE .env
```

#### 3. Gemini API errors
```bash
# Test API key
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
  https://generativelanguage.googleapis.com/v1/models

# Check network connectivity
docker exec ai-agent ping google.com
```

#### 4. Database issues
```bash
# Check ChromaDB directory
ls -la chroma_db/

# Reset database (caution: data loss)
rm -rf chroma_db/*
docker-compose restart ai-agent
```

## 📈 Scaling

### Horizontal Scaling
```yaml
# docker-compose.yml
services:
  ai-agent:
    # ... existing config
    deploy:
      replicas: 3
  
  nginx:
    # Add load balancing configuration
```

### Performance Optimization
- Use Redis for caching
- Implement database connection pooling
- Optimize file processing
- Use CDN for static files
- Monitor and tune resource limits

## 🔄 Updates

### Rolling Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and deploy
docker-compose build --no-cache
docker-compose up -d

# Health check
curl http://localhost:8000/health
```

### Backup Strategy
```bash
# Backup uploads and database
tar -czf backup-$(date +%Y%m%d).tar.gz uploads/ chroma_db/ .env

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "$BACKUP_DIR/ai-agent-backup-$DATE.tar.gz" uploads/ chroma_db/ .env
find "$BACKUP_DIR" -name "ai-agent-backup-*.tar.gz" -mtime +7 -delete
```
