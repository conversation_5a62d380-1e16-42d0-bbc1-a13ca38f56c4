# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# Environment variables
.env

# Database
*.db
*.sqlite3

# Uploads (will be mounted as volume)
uploads/*
!uploads/.gitkeep

# Vector Database (will be mounted as volume)
chroma_db/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs (will be mounted as volume)
logs/

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
docs/

# Tests
tests/
htmlcov/
.coverage
.pytest_cache/

# Development scripts
setup_dev.py
run_tests.py

# Temporary files
tmp/
temp/
*.tmp

# SSL certificates
ssl/
