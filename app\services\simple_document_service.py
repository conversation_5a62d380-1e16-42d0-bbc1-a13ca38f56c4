"""
Simple document service without ChromaDB dependency
Uses basic text search instead of vector search
"""

import json
import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio

from app.core.config import settings

logger = logging.getLogger(__name__)


class SimpleDocumentService:
    """Simple document service using file-based storage"""
    
    def __init__(self):
        """Khởi tạo simple document service"""
        self.storage_dir = "simple_storage"
        self.documents_file = os.path.join(self.storage_dir, "documents.json")
        
        # Tạo thư mục storage nếu chưa có
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # Load existing documents
        self.documents = self._load_documents()
        
        logger.info("Simple document service initialized")

    def _load_documents(self) -> Dict[str, Any]:
        """Load documents từ file JSON"""
        try:
            if os.path.exists(self.documents_file):
                with open(self.documents_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading documents: {str(e)}")
            return {}

    def _save_documents(self):
        """Save documents to file JSON"""
        try:
            with open(self.documents_file, 'w', encoding='utf-8') as f:
                json.dump(self.documents, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving documents: {str(e)}")

    async def add_document(
        self, 
        file_id: str, 
        filename: str, 
        file_path: str, 
        content: str, 
        file_type: str
    ) -> Dict[str, Any]:
        """Thêm document mới"""
        try:
            # Tạo chunks đơn giản
            chunks = self._create_simple_chunks(content)
            
            # Tạo summary đơn giản
            summary = self._create_simple_summary(content)
            
            # Lưu document info
            document_info = {
                "file_id": file_id,
                "filename": filename,
                "file_path": file_path,
                "file_type": file_type,
                "upload_time": datetime.now().isoformat(),
                "content": content,
                "chunks": chunks,
                "content_length": len(content),
                "chunk_count": len(chunks),
                "short_summary": summary,
                "keywords": self._extract_keywords(content)
            }
            
            self.documents[file_id] = document_info
            self._save_documents()
            
            logger.info(f"Added document {file_id} with {len(chunks)} chunks")
            
            return {
                "file_id": file_id,
                "filename": filename,
                "chunks_added": len(chunks),
                "analysis": {
                    "short_summary": summary,
                    "keywords": document_info["keywords"]
                },
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error adding document {file_id}: {str(e)}")
            raise e

    async def search_documents(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """Tìm kiếm documents bằng text search đơn giản"""
        try:
            query_lower = query.lower()
            results = []
            
            for file_id, doc in self.documents.items():
                content_lower = doc["content"].lower()
                
                # Simple text matching
                if query_lower in content_lower:
                    # Find relevant chunks
                    relevant_chunks = []
                    for chunk in doc["chunks"]:
                        if query_lower in chunk["text"].lower():
                            relevant_chunks.append({
                                "content": chunk["text"],
                                "filename": doc["filename"],
                                "file_type": doc["file_type"],
                                "similarity": 0.8  # Fake similarity score
                            })
                    
                    if relevant_chunks:
                        results.extend(relevant_chunks[:3])  # Max 3 chunks per document
            
            # Sort by relevance (simple keyword count)
            results.sort(key=lambda x: x["content"].lower().count(query_lower), reverse=True)
            results = results[:limit]
            
            if results:
                relevant_content = "\n\n".join([
                    f"[Từ file: {chunk['filename']}]\n{chunk['content']}" 
                    for chunk in results[:5]
                ])
                sources = list(set([chunk['filename'] for chunk in results]))
                
                return {
                    "relevant_content": relevant_content,
                    "sources": sources,
                    "results": results,
                    "total_found": len(results)
                }
            else:
                return {
                    "relevant_content": "",
                    "sources": [],
                    "results": [],
                    "total_found": 0
                }
                
        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            return {
                "relevant_content": "",
                "sources": [],
                "results": [],
                "total_found": 0
            }

    async def get_all_documents(self) -> List[Dict[str, Any]]:
        """Lấy danh sách tất cả documents"""
        try:
            documents_list = []
            for file_id, doc in self.documents.items():
                documents_list.append({
                    "file_id": file_id,
                    "filename": doc["filename"],
                    "file_type": doc["file_type"],
                    "upload_time": doc["upload_time"],
                    "content_length": doc["content_length"],
                    "chunk_count": doc["chunk_count"],
                    "short_summary": doc.get("short_summary", ""),
                    "keywords": doc.get("keywords", [])
                })
            
            return documents_list
            
        except Exception as e:
            logger.error(f"Error getting all documents: {str(e)}")
            return []

    async def get_document(self, file_id: str) -> Optional[Dict[str, Any]]:
        """Lấy thông tin chi tiết của một document"""
        try:
            if file_id in self.documents:
                doc = self.documents[file_id]
                return {
                    "file_id": file_id,
                    "filename": doc["filename"],
                    "file_type": doc["file_type"],
                    "upload_time": doc["upload_time"],
                    "content_length": doc["content_length"],
                    "chunk_count": doc["chunk_count"],
                    "short_summary": doc.get("short_summary", ""),
                    "keywords": doc.get("keywords", []),
                    "chunks": doc.get("chunks", [])
                }
            return None
            
        except Exception as e:
            logger.error(f"Error getting document {file_id}: {str(e)}")
            return None

    async def get_document_content(self, file_id: str) -> Optional[str]:
        """Lấy nội dung đầy đủ của document"""
        try:
            if file_id in self.documents:
                return self.documents[file_id]["content"]
            return None
            
        except Exception as e:
            logger.error(f"Error getting document content {file_id}: {str(e)}")
            return None

    async def delete_document(self, file_id: str) -> bool:
        """Xóa document"""
        try:
            if file_id in self.documents:
                # Xóa file vật lý nếu tồn tại
                file_path = self.documents[file_id].get("file_path")
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)
                
                # Xóa khỏi storage
                del self.documents[file_id]
                self._save_documents()
                
                logger.info(f"Deleted document {file_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting document {file_id}: {str(e)}")
            return False

    def _create_simple_chunks(self, content: str, chunk_size: int = 1000) -> List[Dict[str, Any]]:
        """Chia nội dung thành chunks đơn giản"""
        chunks = []
        start = 0
        
        while start < len(content):
            end = min(start + chunk_size, len(content))
            
            # Tìm điểm cắt tốt
            if end < len(content):
                for i in range(end, max(start + chunk_size - 100, start), -1):
                    if content[i] in '.!?\n':
                        end = i + 1
                        break
            
            chunk_text = content[start:end].strip()
            if chunk_text:
                chunks.append({
                    "text": chunk_text,
                    "start": start,
                    "end": end
                })
            
            start = end
        
        return chunks

    def _create_simple_summary(self, content: str) -> str:
        """Tạo summary đơn giản"""
        # Lấy 200 ký tự đầu làm summary
        summary = content[:200].strip()
        if len(content) > 200:
            summary += "..."
        return summary

    def _extract_keywords(self, content: str) -> List[str]:
        """Trích xuất keywords đơn giản"""
        # Simple keyword extraction
        import re
        
        # Remove special characters and split into words
        words = re.findall(r'\b\w+\b', content.lower())
        
        # Filter common words (simple stopwords)
        stopwords = {
            'và', 'của', 'trong', 'với', 'là', 'có', 'được', 'để', 'một', 'các',
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'
        }
        
        # Count word frequency
        word_count = {}
        for word in words:
            if len(word) > 3 and word not in stopwords:
                word_count[word] = word_count.get(word, 0) + 1
        
        # Get top 10 most frequent words
        keywords = sorted(word_count.items(), key=lambda x: x[1], reverse=True)[:10]
        return [word for word, count in keywords]
