#!/usr/bin/env python3
"""
Script để chạy tests cho FastAPI Gemini AI Agent
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Chạy command và hiển thị kết quả"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False

def main():
    """Main function"""
    print("🚀 FastAPI Gemini AI Agent - Test Runner")
    
    # Kiểm tra pytest có được cài đặt không
    try:
        import pytest
        print("✅ pytest is installed")
    except ImportError:
        print("❌ pytest is not installed. Please run: pip install -r requirements.txt")
        sys.exit(1)
    
    # Chạy các loại test khác nhau
    test_commands = [
        {
            "command": "python -m pytest tests/ -v",
            "description": "Chạy tất cả tests với verbose output"
        },
        {
            "command": "python -m pytest tests/ --cov=app --cov-report=html --cov-report=term-missing",
            "description": "Chạy tests với coverage report"
        },
        {
            "command": "python -m pytest tests/test_main.py -v",
            "description": "Chạy API tests"
        },
        {
            "command": "python -m pytest tests/test_services.py -v",
            "description": "Chạy service tests"
        }
    ]
    
    # Tạo thư mục reports nếu chưa có
    os.makedirs("reports", exist_ok=True)
    
    success_count = 0
    total_count = len(test_commands)
    
    for test_config in test_commands:
        if run_command(test_config["command"], test_config["description"]):
            success_count += 1
        else:
            print(f"❌ Failed: {test_config['description']}")
    
    # Tóm tắt kết quả
    print(f"\n{'='*60}")
    print(f"📊 TEST SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Successful: {success_count}/{total_count}")
    print(f"❌ Failed: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All tests passed!")
        
        # Hiển thị thông tin coverage nếu có
        coverage_html_path = Path("htmlcov/index.html")
        if coverage_html_path.exists():
            print(f"📈 Coverage report: {coverage_html_path.absolute()}")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
