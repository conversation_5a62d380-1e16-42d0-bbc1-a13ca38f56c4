"""
Pytest configuration and fixtures
"""

import pytest
import tempfile
import os
import shutil
from unittest.mock import patch, MagicMock

# Mock environment variables for testing
@pytest.fixture(autouse=True)
def mock_env_vars():
    """Mock environment variables for all tests"""
    with patch.dict(os.environ, {
        'GEMINI_API_KEY': 'test_api_key',
        'DATABASE_URL': 'sqlite:///./test.db',
        'CHROMA_PERSIST_DIRECTORY': './test_chroma_db',
        'UPLOAD_DIRECTORY': './test_uploads',
        'DEBUG': 'True'
    }):
        yield


@pytest.fixture
def temp_upload_dir():
    """Create temporary upload directory for testing"""
    temp_dir = tempfile.mkdtemp(prefix="test_uploads_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def temp_chroma_dir():
    """Create temporary ChromaDB directory for testing"""
    temp_dir = tempfile.mkdtemp(prefix="test_chroma_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def sample_pdf_file():
    """Create a sample PDF file for testing"""
    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
        # Write some fake PDF content
        tmp_file.write(b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n")
        tmp_file_path = tmp_file.name
    
    yield tmp_file_path
    
    # Cleanup
    if os.path.exists(tmp_file_path):
        os.unlink(tmp_file_path)


@pytest.fixture
def sample_docx_file():
    """Create a sample DOCX file for testing"""
    with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as tmp_file:
        # Write some fake DOCX content (minimal ZIP structure)
        tmp_file.write(b"PK\x03\x04\x14\x00\x00\x00\x08\x00")
        tmp_file_path = tmp_file.name
    
    yield tmp_file_path
    
    # Cleanup
    if os.path.exists(tmp_file_path):
        os.unlink(tmp_file_path)


@pytest.fixture
def sample_image_file():
    """Create a sample image file for testing"""
    with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp_file:
        # Write minimal JPEG header
        tmp_file.write(b"\xff\xd8\xff\xe0\x00\x10JFIF")
        tmp_file_path = tmp_file.name
    
    yield tmp_file_path
    
    # Cleanup
    if os.path.exists(tmp_file_path):
        os.unlink(tmp_file_path)


@pytest.fixture
def mock_gemini_service():
    """Mock GeminiService for testing"""
    with patch('app.services.gemini_service.GeminiService') as mock_service:
        mock_instance = MagicMock()
        mock_instance.generate_response.return_value = "Mocked Gemini response"
        mock_instance.analyze_document_content.return_value = {
            "short_summary": "Test summary",
            "detailed_summary": "Detailed test summary",
            "keywords": ["test", "mock"],
            "main_topic": "Testing"
        }
        mock_instance.health_check.return_value = True
        mock_service.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_document_service():
    """Mock DocumentService for testing"""
    with patch('app.services.document_service.DocumentService') as mock_service:
        mock_instance = MagicMock()
        mock_instance.add_document.return_value = {
            "file_id": "test_file_id",
            "filename": "test.pdf",
            "chunks_added": 5,
            "status": "success"
        }
        mock_instance.search_documents.return_value = {
            "relevant_content": "Test content from documents",
            "sources": ["test.pdf"],
            "results": [{"content": "test", "filename": "test.pdf"}],
            "total_found": 1
        }
        mock_instance.get_all_documents.return_value = [
            {
                "file_id": "test1",
                "filename": "test1.pdf",
                "file_type": ".pdf",
                "upload_time": "2024-01-01T00:00:00",
                "chunk_count": 5
            }
        ]
        mock_instance.get_document.return_value = {
            "file_id": "test1",
            "filename": "test1.pdf",
            "file_type": ".pdf",
            "short_summary": "Test summary",
            "keywords": ["test", "document"]
        }
        mock_instance.delete_document.return_value = True
        mock_service.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_web_search_service():
    """Mock WebSearchService for testing"""
    with patch('app.services.web_search_service.WebSearchService') as mock_service:
        mock_instance = MagicMock()
        mock_instance.search.return_value = {
            "results": [
                {
                    "title": "Test Result",
                    "url": "https://example.com",
                    "snippet": "Test snippet",
                    "content": "Test content from web"
                }
            ],
            "summary": "Web search results summary",
            "total_found": 1
        }
        mock_service.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_file_processor_service():
    """Mock FileProcessorService for testing"""
    with patch('app.services.file_processor_service.FileProcessorService') as mock_service:
        mock_instance = MagicMock()
        mock_instance.process_file.return_value = "Extracted content from file"
        mock_instance.validate_file.return_value = True
        mock_instance.get_file_info.return_value = {
            "file_path": "/test/path",
            "file_name": "test.pdf",
            "file_extension": ".pdf",
            "file_size": 1024,
            "is_supported": True
        }
        mock_instance.get_supported_extensions.return_value = [
            ".pdf", ".docx", ".xlsx", ".jpg", ".mp3", ".mp4"
        ]
        mock_service.return_value = mock_instance
        yield mock_instance


# Async test helpers
@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    import asyncio
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Database fixtures
@pytest.fixture
def clean_test_db():
    """Clean test database before and after tests"""
    test_db_path = "test.db"
    
    # Clean before test
    if os.path.exists(test_db_path):
        os.unlink(test_db_path)
    
    yield
    
    # Clean after test
    if os.path.exists(test_db_path):
        os.unlink(test_db_path)


# ChromaDB fixtures
@pytest.fixture
def mock_chroma_client():
    """Mock ChromaDB client for testing"""
    with patch('chromadb.PersistentClient') as mock_client:
        mock_collection = MagicMock()
        mock_collection.add.return_value = None
        mock_collection.query.return_value = {
            "documents": [["Test document content"]],
            "metadatas": [[{"file_id": "test1", "filename": "test.pdf"}]],
            "distances": [[0.5]]
        }
        mock_collection.get.return_value = {
            "metadatas": [{"file_id": "test1", "filename": "test.pdf"}]
        }
        mock_collection.delete.return_value = None
        
        mock_client_instance = MagicMock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        yield mock_client_instance
