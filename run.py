#!/usr/bin/env python3
"""
FastAPI Gemini AI Agent - Main Runner
<PERSON><PERSON><PERSON><PERSON> dụng AI Agent <PERSON><PERSON> phân tích dữ liệu đa định dạng
"""

import uvicorn
import logging
import sys
import os
from pathlib import Path

# Add app directory to Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

from app.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO if not settings.debug else logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('ai_agent.log')
    ]
)

logger = logging.getLogger(__name__)


def check_environment():
    """Kiểm tra môi trường và cấu hình cần thiết"""
    logger.info("Checking environment configuration...")
    
    # Kiểm tra Gemini API key
    if not settings.gemini_api_key:
        logger.error("GEMINI_API_KEY is not set! Please check your .env file.")
        return False
    
    # Ki<PERSON><PERSON> tra thư mục cần thiết
    required_dirs = [
        settings.upload_directory,
        settings.chroma_persist_directory,
        "static/css",
        "static/js",
        "templates"
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            logger.warning(f"Directory {dir_path} does not exist, creating...")
            os.makedirs(dir_path, exist_ok=True)
    
    logger.info("Environment check completed successfully!")
    return True


def main():
    """Main function to run the application"""
    logger.info("Starting FastAPI Gemini AI Agent...")
    
    # Check environment
    if not check_environment():
        logger.error("Environment check failed. Exiting...")
        sys.exit(1)
    
    # Print startup information
    logger.info(f"App Name: {settings.app_name}")
    logger.info(f"Version: {settings.app_version}")
    logger.info(f"Debug Mode: {settings.debug}")
    logger.info(f"Upload Directory: {settings.upload_directory}")
    logger.info(f"ChromaDB Directory: {settings.chroma_persist_directory}")
    
    # Run the application
    try:
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=settings.debug,
            log_level="info" if not settings.debug else "debug",
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except Exception as e:
        logger.error(f"Error running application: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
