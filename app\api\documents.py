from fastapi import APIRouter, HTTPException
from typing import List, Optional
import logging

from app.services.document_service import DocumentService

router = APIRouter()

# Initialize services
document_service = DocumentService()

logger = logging.getLogger(__name__)


@router.get("/")
async def list_documents():
    """L<PERSON>y danh sách tất cả documents đã upload"""
    try:
        documents = await document_service.get_all_documents()
        return {"documents": documents}
    except Exception as e:
        logger.error(f"Error listing documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi lấy danh sách documents: {str(e)}")


@router.get("/{file_id}")
async def get_document(file_id: str):
    """Lấy thông tin chi tiết của một document"""
    try:
        document = await document_service.get_document(file_id)
        if document:
            return document
        else:
            raise HTTPException(status_code=404, detail="Không tìm thấy document")
    except Exception as e:
        logger.error(f"Error getting document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi lấy thông tin document: {str(e)}")


@router.post("/search")
async def search_documents(query: str, limit: Optional[int] = 10):
    """Tìm kiếm trong các documents đã upload"""
    try:
        results = await document_service.search_documents(query, limit=limit)
        return results
    except Exception as e:
        logger.error(f"Error searching documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi tìm kiếm documents: {str(e)}")


@router.get("/{file_id}/content")
async def get_document_content(file_id: str):
    """Lấy nội dung đầy đủ của document"""
    try:
        content = await document_service.get_document_content(file_id)
        if content:
            return {"file_id": file_id, "content": content}
        else:
            raise HTTPException(status_code=404, detail="Không tìm thấy nội dung document")
    except Exception as e:
        logger.error(f"Error getting document content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi lấy nội dung document: {str(e)}")


@router.delete("/{file_id}")
async def delete_document(file_id: str):
    """Xóa document"""
    try:
        success = await document_service.delete_document(file_id)
        if success:
            return {"message": "Document đã được xóa thành công"}
        else:
            raise HTTPException(status_code=404, detail="Không tìm thấy document")
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi xóa document: {str(e)}")
