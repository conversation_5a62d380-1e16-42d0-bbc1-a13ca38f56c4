from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    # Application settings
    app_name: str = "FastAPI Gemini AI Agent"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # Gemini AI settings
    gemini_api_key: str
    
    # Database settings
    database_url: str = "sqlite:///./ai_agent.db"
    
    # File upload settings
    max_file_size: int = 50 * 1024 * 1024  # 50MB in bytes
    allowed_extensions: List[str] = [
        ".pdf", ".docx", ".xlsx", ".xls", 
        ".jpg", ".jpeg", ".png", ".gif",
        ".mp3", ".wav", ".mp4", ".avi", ".mov"
    ]
    upload_directory: str = "uploads"
    
    # Vector database settings
    chroma_persist_directory: str = "./chroma_db"
    
    # Web search settings (optional)
    google_search_api_key: str = ""
    google_search_engine_id: str = ""
    
    # CORS settings
    allowed_origins: List[str] = ["*"]
    allowed_methods: List[str] = ["*"]
    allowed_headers: List[str] = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Create settings instance
settings = Settings()

# Ensure upload directory exists
os.makedirs(settings.upload_directory, exist_ok=True)
os.makedirs(settings.chroma_persist_directory, exist_ok=True)
