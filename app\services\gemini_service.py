import google.generativeai as genai
from typing import Optional, List, Dict, Any
import logging
import asyncio
from functools import wraps

from app.core.config import settings

logger = logging.getLogger(__name__)


def async_retry(max_retries: int = 3, delay: float = 1.0):
    """Decorator để retry async functions"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    logger.warning(f"Attempt {attempt + 1} failed: {str(e)}. Retrying...")
                    await asyncio.sleep(delay * (attempt + 1))
            return None
        return wrapper
    return decorator


class GeminiService:
    def __init__(self):
        """Khởi tạo Gemini service"""
        try:
            # Configure Gemini API
            genai.configure(api_key=settings.gemini_api_key)
            
            # Initialize model
            self.model = genai.GenerativeModel('gemini-pro')
            
            # Model configuration
            self.generation_config = {
                'temperature': 0.7,
                'top_p': 0.8,
                'top_k': 40,
                'max_output_tokens': 2048,
            }
            
            # Safety settings
            self.safety_settings = [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
            
            logger.info("Gemini service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini service: {str(e)}")
            raise e

    @async_retry(max_retries=3, delay=1.0)
    async def generate_response(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Tạo response từ Gemini AI
        
        Args:
            prompt: Câu hỏi hoặc yêu cầu từ người dùng
            context: Thông tin bổ sung để cung cấp context
            
        Returns:
            str: Response từ Gemini AI
        """
        try:
            # Xây dựng prompt đầy đủ
            full_prompt = self._build_prompt(prompt, context)
            
            # Gọi Gemini API
            response = await asyncio.to_thread(
                self.model.generate_content,
                full_prompt,
                generation_config=self.generation_config,
                safety_settings=self.safety_settings
            )
            
            # Kiểm tra response
            if response.candidates and len(response.candidates) > 0:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and candidate.content.parts:
                    return candidate.content.parts[0].text
            
            # Fallback nếu không có response hợp lệ
            return "Xin lỗi, tôi không thể tạo ra câu trả lời phù hợp cho câu hỏi này."
            
        except Exception as e:
            logger.error(f"Error generating response from Gemini: {str(e)}")
            raise e

    def _build_prompt(self, user_prompt: str, context: Optional[str] = None) -> str:
        """
        Xây dựng prompt đầy đủ cho Gemini
        
        Args:
            user_prompt: Câu hỏi từ người dùng
            context: Context bổ sung
            
        Returns:
            str: Prompt đầy đủ
        """
        system_prompt = """
        Bạn là một AI Assistant chuyên nghiệp, thông minh và hữu ích. 
        Bạn có khả năng phân tích và trả lời các câu hỏi dựa trên thông tin được cung cấp.
        
        Hướng dẫn:
        1. Trả lời bằng tiếng Việt một cách tự nhiên và dễ hiểu
        2. Nếu có thông tin context, hãy ưu tiên sử dụng thông tin đó
        3. Nếu không có đủ thông tin, hãy nói rõ và đưa ra câu trả lời tốt nhất có thể
        4. Trả lời một cách chi tiết và có cấu trúc
        5. Sử dụng markdown để format câu trả lời khi cần thiết
        """
        
        if context:
            full_prompt = f"""
            {system_prompt}
            
            Thông tin tham khảo:
            {context}
            
            Câu hỏi của người dùng:
            {user_prompt}
            
            Hãy trả lời dựa trên thông tin tham khảo ở trên.
            """
        else:
            full_prompt = f"""
            {system_prompt}
            
            Câu hỏi của người dùng:
            {user_prompt}
            """
        
        return full_prompt

    async def analyze_document_content(self, content: str, file_type: str) -> Dict[str, Any]:
        """
        Phân tích nội dung document và tạo summary
        
        Args:
            content: Nội dung document
            file_type: Loại file (.pdf, .docx, etc.)
            
        Returns:
            Dict chứa summary và keywords
        """
        try:
            prompt = f"""
            Hãy phân tích nội dung document sau và tạo:
            1. Tóm tắt ngắn gọn (2-3 câu)
            2. Tóm tắt chi tiết (1 đoạn văn)
            3. Các từ khóa chính (5-10 từ khóa)
            4. Chủ đề chính
            
            Loại file: {file_type}
            
            Nội dung:
            {content[:4000]}  # Giới hạn để tránh quá dài
            
            Trả lời theo format JSON:
            {{
                "short_summary": "...",
                "detailed_summary": "...",
                "keywords": ["keyword1", "keyword2", ...],
                "main_topic": "..."
            }}
            """
            
            response = await self.generate_response(prompt)
            
            # Parse JSON response (có thể cần xử lý thêm)
            import json
            try:
                analysis = json.loads(response)
                return analysis
            except json.JSONDecodeError:
                # Fallback nếu không parse được JSON
                return {
                    "short_summary": response[:200],
                    "detailed_summary": response,
                    "keywords": [],
                    "main_topic": "Không xác định"
                }
                
        except Exception as e:
            logger.error(f"Error analyzing document content: {str(e)}")
            return {
                "short_summary": "Không thể phân tích nội dung",
                "detailed_summary": "Có lỗi xảy ra khi phân tích document",
                "keywords": [],
                "main_topic": "Lỗi"
            }

    async def health_check(self) -> bool:
        """Kiểm tra trạng thái của Gemini service"""
        try:
            test_response = await self.generate_response("Hello")
            return bool(test_response and len(test_response) > 0)
        except Exception:
            return False
