from typing import Dict, List, Optional, Any
import logging
import asyncio
import json
import os
from datetime import datetime

from app.core.config import settings

logger = logging.getLogger(__name__)

# Try to import advanced dependencies, fallback to simple version
try:
    import chromadb
    from chromadb.config import Settings
    from sentence_transformers import SentenceTransformer
    ADVANCED_FEATURES = True
    logger.info("Using advanced document service with ChromaDB")
except ImportError:
    ADVANCED_FEATURES = False
    logger.warning("ChromaDB not available, using simple document service")

# Import Gemini service with fallback
try:
    from app.services.gemini_service import GeminiService
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger.warning("Gemini service not available")


class DocumentService:
    """Service để lưu trữ và tìm kiếm documents sử dụng ChromaDB"""
    
    def __init__(self):
        """Khởi tạo document service"""
        try:
            # Khởi tạo ChromaDB client
            self.chroma_client = chromadb.PersistentClient(
                path=settings.chroma_persist_directory,
                settings=Settings(anonymized_telemetry=False)
            )
            
            # Tạo hoặc lấy collection
            self.collection = self.chroma_client.get_or_create_collection(
                name="documents",
                metadata={"description": "Document storage for AI Agent"}
            )
            
            # Khởi tạo sentence transformer cho embedding
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Khởi tạo Gemini service để phân tích documents
            self.gemini_service = GeminiService()
            
            logger.info("Document service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize document service: {str(e)}")
            raise e

    async def add_document(
        self, 
        file_id: str, 
        filename: str, 
        file_path: str, 
        content: str, 
        file_type: str
    ) -> Dict[str, Any]:
        """
        Thêm document mới vào hệ thống
        
        Args:
            file_id: ID unique của file
            filename: Tên file gốc
            file_path: Đường dẫn đến file
            content: Nội dung đã được extract
            file_type: Loại file (.pdf, .docx, etc.)
            
        Returns:
            Dict chứa thông tin document đã được thêm
        """
        try:
            # Phân tích nội dung với Gemini
            analysis = await self.gemini_service.analyze_document_content(content, file_type)
            
            # Tạo chunks từ nội dung để tìm kiếm tốt hơn
            chunks = await self._create_chunks(content)
            
            # Tạo embeddings cho từng chunk
            chunk_embeddings = await self._create_embeddings([chunk["text"] for chunk in chunks])
            
            # Metadata cho document
            document_metadata = {
                "file_id": file_id,
                "filename": filename,
                "file_path": file_path,
                "file_type": file_type,
                "upload_time": datetime.now().isoformat(),
                "content_length": len(content),
                "chunk_count": len(chunks),
                "short_summary": analysis.get("short_summary", ""),
                "main_topic": analysis.get("main_topic", ""),
                "keywords": json.dumps(analysis.get("keywords", []))
            }
            
            # Thêm từng chunk vào ChromaDB
            chunk_ids = []
            chunk_texts = []
            chunk_metadatas = []
            
            for i, chunk in enumerate(chunks):
                chunk_id = f"{file_id}_chunk_{i}"
                chunk_ids.append(chunk_id)
                chunk_texts.append(chunk["text"])
                
                chunk_metadata = document_metadata.copy()
                chunk_metadata.update({
                    "chunk_id": chunk_id,
                    "chunk_index": i,
                    "chunk_start": chunk["start"],
                    "chunk_end": chunk["end"]
                })
                chunk_metadatas.append(chunk_metadata)
            
            # Thêm vào ChromaDB
            def add_to_chroma():
                self.collection.add(
                    ids=chunk_ids,
                    documents=chunk_texts,
                    embeddings=chunk_embeddings,
                    metadatas=chunk_metadatas
                )
            
            await asyncio.to_thread(add_to_chroma)
            
            logger.info(f"Added document {file_id} with {len(chunks)} chunks")
            
            return {
                "file_id": file_id,
                "filename": filename,
                "chunks_added": len(chunks),
                "analysis": analysis,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error adding document {file_id}: {str(e)}")
            raise e

    async def search_documents(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm documents dựa trên query
        
        Args:
            query: Câu hỏi hoặc từ khóa tìm kiếm
            limit: Số lượng kết quả tối đa
            
        Returns:
            Dict chứa kết quả tìm kiếm
        """
        try:
            # Tạo embedding cho query
            query_embedding = await self._create_embeddings([query])
            
            # Tìm kiếm trong ChromaDB
            def search_chroma():
                results = self.collection.query(
                    query_embeddings=query_embedding,
                    n_results=limit,
                    include=["documents", "metadatas", "distances"]
                )
                return results
            
            search_results = await asyncio.to_thread(search_chroma)
            
            if not search_results["documents"] or not search_results["documents"][0]:
                return {
                    "relevant_content": "",
                    "sources": [],
                    "results": []
                }
            
            # Xử lý kết quả
            relevant_chunks = []
            sources = set()
            
            for i, (doc, metadata, distance) in enumerate(zip(
                search_results["documents"][0],
                search_results["metadatas"][0], 
                search_results["distances"][0]
            )):
                # Chỉ lấy kết quả có độ tương đồng cao (distance thấp)
                if distance < 0.8:  # Threshold có thể điều chỉnh
                    relevant_chunks.append({
                        "content": doc,
                        "filename": metadata["filename"],
                        "file_type": metadata["file_type"],
                        "similarity": 1 - distance,
                        "chunk_index": metadata["chunk_index"]
                    })
                    sources.add(metadata["filename"])
            
            # Tổng hợp nội dung relevant
            relevant_content = "\n\n".join([
                f"[Từ file: {chunk['filename']}]\n{chunk['content']}" 
                for chunk in relevant_chunks[:5]  # Giới hạn 5 chunks tốt nhất
            ])
            
            return {
                "relevant_content": relevant_content,
                "sources": list(sources),
                "results": relevant_chunks,
                "total_found": len(relevant_chunks)
            }
            
        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            return {
                "relevant_content": "",
                "sources": [],
                "results": []
            }

    async def get_all_documents(self) -> List[Dict[str, Any]]:
        """Lấy danh sách tất cả documents"""
        try:
            def get_docs():
                # Lấy tất cả documents, nhóm theo file_id
                results = self.collection.get(include=["metadatas"])
                
                documents = {}
                for metadata in results["metadatas"]:
                    file_id = metadata["file_id"]
                    if file_id not in documents:
                        documents[file_id] = {
                            "file_id": file_id,
                            "filename": metadata["filename"],
                            "file_type": metadata["file_type"],
                            "upload_time": metadata["upload_time"],
                            "content_length": metadata["content_length"],
                            "chunk_count": metadata["chunk_count"],
                            "short_summary": metadata.get("short_summary", ""),
                            "main_topic": metadata.get("main_topic", ""),
                            "keywords": json.loads(metadata.get("keywords", "[]"))
                        }
                
                return list(documents.values())
            
            return await asyncio.to_thread(get_docs)
            
        except Exception as e:
            logger.error(f"Error getting all documents: {str(e)}")
            return []

    async def get_document(self, file_id: str) -> Optional[Dict[str, Any]]:
        """Lấy thông tin chi tiết của một document"""
        try:
            def get_doc():
                results = self.collection.get(
                    where={"file_id": file_id},
                    include=["documents", "metadatas"]
                )
                
                if not results["metadatas"]:
                    return None
                
                # Lấy metadata từ chunk đầu tiên
                first_metadata = results["metadatas"][0]
                
                # Tổng hợp nội dung từ tất cả chunks
                chunks = []
                for doc, metadata in zip(results["documents"], results["metadatas"]):
                    chunks.append({
                        "chunk_index": metadata["chunk_index"],
                        "content": doc,
                        "chunk_start": metadata["chunk_start"],
                        "chunk_end": metadata["chunk_end"]
                    })
                
                # Sắp xếp chunks theo thứ tự
                chunks.sort(key=lambda x: x["chunk_index"])
                
                return {
                    "file_id": file_id,
                    "filename": first_metadata["filename"],
                    "file_type": first_metadata["file_type"],
                    "upload_time": first_metadata["upload_time"],
                    "content_length": first_metadata["content_length"],
                    "chunk_count": len(chunks),
                    "short_summary": first_metadata.get("short_summary", ""),
                    "main_topic": first_metadata.get("main_topic", ""),
                    "keywords": json.loads(first_metadata.get("keywords", "[]")),
                    "chunks": chunks
                }
            
            return await asyncio.to_thread(get_doc)

        except Exception as e:
            logger.error(f"Error getting document {file_id}: {str(e)}")
            return None

    async def get_document_content(self, file_id: str) -> Optional[str]:
        """Lấy nội dung đầy đủ của document"""
        try:
            document = await self.get_document(file_id)
            if document and "chunks" in document:
                return "\n".join([chunk["content"] for chunk in document["chunks"]])
            return None

        except Exception as e:
            logger.error(f"Error getting document content {file_id}: {str(e)}")
            return None

    async def delete_document(self, file_id: str) -> bool:
        """Xóa document khỏi hệ thống"""
        try:
            def delete_doc():
                # Lấy tất cả chunk IDs của document
                results = self.collection.get(
                    where={"file_id": file_id},
                    include=["metadatas"]
                )

                if not results["metadatas"]:
                    return False

                # Lấy chunk IDs
                chunk_ids = [metadata["chunk_id"] for metadata in results["metadatas"]]

                # Xóa khỏi ChromaDB
                self.collection.delete(ids=chunk_ids)

                # Xóa file vật lý nếu tồn tại
                file_path = results["metadatas"][0].get("file_path")
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)

                return True

            success = await asyncio.to_thread(delete_doc)

            if success:
                logger.info(f"Deleted document {file_id}")

            return success

        except Exception as e:
            logger.error(f"Error deleting document {file_id}: {str(e)}")
            return False

    async def _create_chunks(self, content: str, chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
        """Chia nội dung thành các chunks nhỏ hơn"""
        try:
            chunks = []
            start = 0

            while start < len(content):
                end = min(start + chunk_size, len(content))

                # Tìm điểm cắt tốt (cuối câu hoặc đoạn)
                if end < len(content):
                    # Tìm dấu chấm câu gần nhất
                    for i in range(end, max(start + chunk_size - 100, start), -1):
                        if content[i] in '.!?\n':
                            end = i + 1
                            break

                chunk_text = content[start:end].strip()
                if chunk_text:
                    chunks.append({
                        "text": chunk_text,
                        "start": start,
                        "end": end
                    })

                start = max(start + chunk_size - overlap, end)

            return chunks

        except Exception as e:
            logger.error(f"Error creating chunks: {str(e)}")
            return [{"text": content, "start": 0, "end": len(content)}]

    async def _create_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Tạo embeddings cho danh sách texts"""
        try:
            def create_emb():
                embeddings = self.embedding_model.encode(texts)
                return embeddings.tolist()

            return await asyncio.to_thread(create_emb)

        except Exception as e:
            logger.error(f"Error creating embeddings: {str(e)}")
            # Fallback: trả về embeddings rỗng
            return [[0.0] * 384 for _ in texts]  # 384 là dimension của all-MiniLM-L6-v2

    async def get_statistics(self) -> Dict[str, Any]:
        """Lấy thống kê về documents trong hệ thống"""
        try:
            def get_stats():
                results = self.collection.get(include=["metadatas"])

                if not results["metadatas"]:
                    return {
                        "total_documents": 0,
                        "total_chunks": 0,
                        "file_types": {},
                        "total_content_length": 0
                    }

                # Thống kê
                documents = {}
                file_types = {}
                total_content_length = 0

                for metadata in results["metadatas"]:
                    file_id = metadata["file_id"]
                    file_type = metadata["file_type"]

                    if file_id not in documents:
                        documents[file_id] = True
                        total_content_length += metadata.get("content_length", 0)

                    file_types[file_type] = file_types.get(file_type, 0) + 1

                return {
                    "total_documents": len(documents),
                    "total_chunks": len(results["metadatas"]),
                    "file_types": file_types,
                    "total_content_length": total_content_length
                }

            return await asyncio.to_thread(get_stats)

        except Exception as e:
            logger.error(f"Error getting statistics: {str(e)}")
            return {"total_documents": 0, "total_chunks": 0}
