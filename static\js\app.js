// Global variables
let currentSessionId = generateSessionId();
let isProcessing = false;
let currentFileId = null;

// DOM elements
const chatContainer = document.getElementById('chatContainer');
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const uploadProgress = document.getElementById('uploadProgress');
const documentsList = document.getElementById('documentsList');
const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
const filePreviewModal = new bootstrap.Modal(document.getElementById('filePreviewModal'));
const notificationToast = new bootstrap.Toast(document.getElementById('notificationToast'));

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadDocuments();
    loadStatistics();
    
    // Focus on message input
    messageInput.focus();
});

// Event listeners
function initializeEventListeners() {
    // Send message
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // File upload
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
    
    // Example questions
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('example-question')) {
            const question = e.target.getAttribute('data-question');
            messageInput.value = question;
            sendMessage();
        }
    });
    
    // Clear chat
    document.getElementById('clearChat').addEventListener('click', clearChat);
    
    // Refresh documents
    document.getElementById('refreshDocs').addEventListener('click', function() {
        loadDocuments();
        loadStatistics();
    });
    
    // Delete file
    document.getElementById('deleteFileBtn').addEventListener('click', deleteCurrentFile);
}

// Message handling
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isProcessing) return;
    
    isProcessing = true;
    sendButton.disabled = true;
    messageInput.disabled = true;
    
    // Add user message to chat
    addMessage('user', message);
    messageInput.value = '';
    
    // Show typing indicator
    const typingId = addTypingIndicator();
    
    try {
        const response = await fetch('/api/chat/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                session_id: currentSessionId
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Remove typing indicator
        removeTypingIndicator(typingId);
        
        // Add assistant response
        addMessage('assistant', data.response, data.sources, data.source_type);
        
    } catch (error) {
        console.error('Error sending message:', error);
        removeTypingIndicator(typingId);
        addMessage('assistant', 'Xin lỗi, có lỗi xảy ra khi xử lý tin nhắn của bạn. Vui lòng thử lại.', [], 'error');
        showNotification('Có lỗi xảy ra khi gửi tin nhắn', 'error');
    } finally {
        isProcessing = false;
        sendButton.disabled = false;
        messageInput.disabled = false;
        messageInput.focus();
    }
}

function addMessage(sender, content, sources = [], sourceType = '') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    
    // Format content with markdown-like styling
    const formattedContent = formatMessageContent(content);
    messageContent.innerHTML = formattedContent;
    
    // Add sources if available
    if (sources && sources.length > 0) {
        const sourcesDiv = document.createElement('div');
        sourcesDiv.className = 'message-sources';
        sourcesDiv.innerHTML = '<strong>Nguồn:</strong> ';
        
        sources.forEach(source => {
            const sourceBadge = document.createElement('span');
            sourceBadge.className = `source-badge ${sourceType}`;
            sourceBadge.textContent = source;
            sourcesDiv.appendChild(sourceBadge);
        });
        
        messageContent.appendChild(sourcesDiv);
    }
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);
    
    // Remove welcome message if exists
    const welcomeMessage = chatContainer.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }
    
    chatContainer.appendChild(messageDiv);
    scrollToBottom();
}

function addTypingIndicator() {
    const typingId = 'typing-' + Date.now();
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message assistant';
    messageDiv.id = typingId;
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = '<i class="fas fa-robot"></i>';
    
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message-content';
    typingDiv.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(typingDiv);
    
    chatContainer.appendChild(messageDiv);
    scrollToBottom();
    
    return typingId;
}

function removeTypingIndicator(typingId) {
    const typingElement = document.getElementById(typingId);
    if (typingElement) {
        typingElement.remove();
    }
}

function formatMessageContent(content) {
    // Simple markdown-like formatting
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>')
        .replace(/```(.*?)```/gs, '<pre><code>$1</code></pre>');
}

function scrollToBottom() {
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function clearChat() {
    const messages = chatContainer.querySelectorAll('.message');
    messages.forEach(message => message.remove());
    
    // Add welcome message back
    const welcomeDiv = document.createElement('div');
    welcomeDiv.className = 'welcome-message';
    welcomeDiv.innerHTML = `
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                <h5>Chat đã được xóa!</h5>
                <p class="text-muted">Bạn có thể bắt đầu cuộc trò chuyện mới.</p>
            </div>
        </div>
    `;
    chatContainer.appendChild(welcomeDiv);
    
    // Generate new session ID
    currentSessionId = generateSessionId();
}

// File upload handling
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = Array.from(e.dataTransfer.files);
    uploadFiles(files);
}

function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    uploadFiles(files);
    e.target.value = ''; // Reset input
}

async function uploadFiles(files) {
    if (files.length === 0) return;
    
    uploadProgress.style.display = 'block';
    const progressBar = uploadProgress.querySelector('.progress-bar');
    const statusText = document.getElementById('uploadStatus');
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const progress = ((i + 1) / files.length) * 100;
        
        progressBar.style.width = progress + '%';
        statusText.textContent = `Đang upload ${file.name}...`;
        
        try {
            await uploadSingleFile(file);
            showNotification(`Upload thành công: ${file.name}`, 'success');
        } catch (error) {
            console.error('Upload error:', error);
            showNotification(`Lỗi upload: ${file.name}`, 'error');
        }
    }
    
    uploadProgress.style.display = 'none';
    loadDocuments();
    loadStatistics();
}

async function uploadSingleFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/upload/file', {
        method: 'POST',
        body: formData
    });
    
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Upload failed');
    }
    
    return await response.json();
}

// Documents management
async function loadDocuments() {
    try {
        const response = await fetch('/api/documents/');
        const data = await response.json();
        
        displayDocuments(data.documents);
    } catch (error) {
        console.error('Error loading documents:', error);
    }
}

function displayDocuments(documents) {
    if (!documents || documents.length === 0) {
        documentsList.innerHTML = '<div class="text-muted text-center"><small>Chưa có document nào</small></div>';
        return;
    }
    
    documentsList.innerHTML = '';
    
    documents.forEach(doc => {
        const docItem = document.createElement('div');
        docItem.className = 'document-item';
        docItem.onclick = () => showDocumentPreview(doc.file_id);
        
        docItem.innerHTML = `
            <div class="doc-name">${doc.filename}</div>
            <div class="doc-info">
                ${doc.file_type} • ${doc.chunk_count} chunks
                <br><small>${formatDate(doc.upload_time)}</small>
            </div>
        `;
        
        documentsList.appendChild(docItem);
    });
}

async function showDocumentPreview(fileId) {
    currentFileId = fileId;
    
    try {
        const response = await fetch(`/api/documents/${fileId}`);
        const doc = await response.json();
        
        document.getElementById('filePreviewTitle').textContent = doc.filename;
        
        const previewBody = document.getElementById('filePreviewBody');
        previewBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Thông tin file</h6>
                    <ul class="list-unstyled">
                        <li><strong>Tên:</strong> ${doc.filename}</li>
                        <li><strong>Loại:</strong> ${doc.file_type}</li>
                        <li><strong>Upload:</strong> ${formatDate(doc.upload_time)}</li>
                        <li><strong>Chunks:</strong> ${doc.chunk_count}</li>
                        <li><strong>Chủ đề:</strong> ${doc.main_topic || 'Không xác định'}</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Từ khóa</h6>
                    <div class="keywords">
                        ${doc.keywords.map(keyword => `<span class="badge bg-secondary me-1">${keyword}</span>`).join('')}
                    </div>
                </div>
            </div>
            <hr>
            <h6>Tóm tắt</h6>
            <p>${doc.short_summary || 'Không có tóm tắt'}</p>
        `;
        
        filePreviewModal.show();
    } catch (error) {
        console.error('Error loading document preview:', error);
        showNotification('Không thể tải thông tin document', 'error');
    }
}

async function deleteCurrentFile() {
    if (!currentFileId) return;
    
    if (!confirm('Bạn có chắc chắn muốn xóa file này?')) return;
    
    try {
        const response = await fetch(`/api/documents/${currentFileId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showNotification('Đã xóa file thành công', 'success');
            filePreviewModal.hide();
            loadDocuments();
            loadStatistics();
        } else {
            throw new Error('Delete failed');
        }
    } catch (error) {
        console.error('Error deleting file:', error);
        showNotification('Không thể xóa file', 'error');
    }
}

// Statistics
async function loadStatistics() {
    try {
        const response = await fetch('/api/documents/');
        const data = await response.json();
        
        const totalDocs = data.documents ? data.documents.length : 0;
        const totalChunks = data.documents ? data.documents.reduce((sum, doc) => sum + doc.chunk_count, 0) : 0;
        
        document.getElementById('totalDocs').textContent = totalDocs;
        document.getElementById('totalChunks').textContent = totalChunks;
    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

// Utility functions
function generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN') + ' ' + date.toLocaleTimeString('vi-VN', {hour: '2-digit', minute: '2-digit'});
}

function showNotification(message, type = 'info') {
    const toastElement = document.getElementById('notificationToast');
    const toastMessage = document.getElementById('toastMessage');
    const toastHeader = toastElement.querySelector('.toast-header i');
    
    toastMessage.textContent = message;
    
    // Update icon based on type
    toastHeader.className = `fas me-2 ${type === 'success' ? 'fa-check-circle text-success' : 
                                      type === 'error' ? 'fa-exclamation-circle text-danger' : 
                                      'fa-info-circle text-primary'}`;
    
    notificationToast.show();
}
