#!/usr/bin/env python3
"""
Script để restart app với kiểm tra API
"""

import subprocess
import sys
import os
import time

def check_api_key():
    """Kiểm tra API key"""
    try:
        with open('.env', 'r') as f:
            content = f.read()
            for line in content.split('\n'):
                if line.startswith('GEMINI_API_KEY='):
                    api_key = line.split('=', 1)[1].strip()
                    if api_key and api_key != "your_api_key_here":
                        return True, api_key
        return False, None
    except:
        return False, None

def main():
    """Main function"""
    print("🔄 Restarting AI Agent...")
    print("=" * 40)
    
    # Check API key
    has_key, api_key = check_api_key()
    
    if not has_key:
        print("❌ API key chưa được cấu hình")
        print("\n💡 Chạy script sửa API:")
        print("python fix_api.py")
        
        fix_now = input("\nChạy fix_api.py ngay? (y/n): ")
        if fix_now.lower() == 'y':
            try:
                subprocess.run([sys.executable, "fix_api.py"])
                
                # Check again
                has_key, api_key = check_api_key()
                if not has_key:
                    print("❌ Vẫn chưa có API key hợp lệ")
                    return
            except Exception as e:
                print(f"❌ Lỗi chạy fix_api.py: {e}")
                return
    
    print(f"✅ API key: {api_key[:20]}...")
    
    # Start app
    print("\n🚀 Starting app...")
    print("🌐 URL: http://localhost:8000")
    print("⏹️  Press Ctrl+C to stop")
    print("-" * 40)
    
    try:
        subprocess.run([sys.executable, "app_simple.py"])
    except KeyboardInterrupt:
        print("\n👋 App stopped")
    except Exception as e:
        print(f"\n❌ Lỗi chạy app: {e}")

if __name__ == "__main__":
    main()
