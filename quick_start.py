#!/usr/bin/env python3
"""
Quick start script - Cài đặt và chạy phiên bản đơn giản
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run command and return success status"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed: {e.stderr}")
        return False

def check_python():
    """Check Python version"""
    version = sys.version_info
    print(f"🐍 Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 7:
        print("✅ Python version OK")
        return True
    else:
        print("❌ Need Python 3.7+")
        return False

def install_basic_deps():
    """Install basic dependencies"""
    basic_packages = [
        "fastapi",
        "uvicorn",
        "python-multipart", 
        "jinja2",
        "python-dotenv",
        "requests"
    ]
    
    print("📦 Installing basic dependencies...")
    
    # Upgrade pip first
    run_command("python -m pip install --upgrade pip", "Upgrading pip")
    
    # Install packages one by one
    failed = []
    for package in basic_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            failed.append(package)
    
    if failed:
        print(f"\n❌ Failed to install: {', '.join(failed)}")
        print("💡 Try installing manually:")
        for package in failed:
            print(f"   pip install {package}")
        return False
    
    print("✅ All basic dependencies installed!")
    return True

def create_env_file():
    """Create .env file"""
    if not os.path.exists('.env'):
        print("📄 Creating .env file...")
        with open('.env', 'w') as f:
            f.write("# Add your Gemini API key here\n")
            f.write("GEMINI_API_KEY=your_api_key_here\n")
        print("✅ .env file created")
        print("⚠️  Please edit .env and add your GEMINI_API_KEY")
    else:
        print("✅ .env file already exists")

def test_imports():
    """Test basic imports"""
    print("🧪 Testing imports...")
    
    test_modules = [
        "fastapi",
        "uvicorn", 
        "requests"
    ]
    
    failed = []
    for module in test_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module}")
            failed.append(module)
    
    return len(failed) == 0

def main():
    """Main function"""
    print("🚀 FastAPI Gemini AI Agent - Quick Start")
    print("=" * 50)
    
    # Check Python
    if not check_python():
        return False
    
    # Install dependencies
    if not install_basic_deps():
        return False
    
    # Create .env file
    create_env_file()
    
    # Test imports
    if not test_imports():
        print("❌ Some imports failed")
        return False
    
    print("\n🎉 Setup completed!")
    print("\n📋 Next steps:")
    print("1. Edit .env file and add your GEMINI_API_KEY")
    print("2. Run: python app_simple.py")
    print("3. Open: http://localhost:8000")
    
    print("\n💡 Features available:")
    print("✅ File upload (text files work best)")
    print("✅ Chat with Gemini AI")
    print("✅ Simple document search")
    print("✅ Web interface")
    
    # Ask if user wants to run now
    response = input("\n🚀 Run the app now? (y/n): ")
    if response.lower() == 'y':
        print("\n🌟 Starting app...")
        try:
            subprocess.run([sys.executable, "app_simple.py"])
        except KeyboardInterrupt:
            print("\n👋 App stopped")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
