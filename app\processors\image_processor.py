from PIL import Image, ExifTags
import cv2
import numpy as np
from typing import Optional, Dict, Any
import logging
import asyncio
import base64
import io

logger = logging.getLogger(__name__)


class ImageProcessor:
    """Processor để xử lý file hình ảnh (.jpg, .jpeg, .png, .gif)"""
    
    async def extract_content(self, file_path: str) -> str:
        """
        Trích xuất thông tin từ hình ảnh
        
        Args:
            file_path: Đường dẫn đến file hình ảnh
            
        Returns:
            str: <PERSON>ô tả về hình ảnh và metadata
        """
        try:
            def extract():
                content_parts = []
                
                # Mở hình ảnh với PIL
                with Image.open(file_path) as img:
                    # Thông tin cơ bản
                    content_parts.append(f"=== Thông tin hình ảnh ===")
                    content_parts.append(f"Kích thước: {img.size[0]} x {img.size[1]} pixels")
                    content_parts.append(f"Định dạng: {img.format}")
                    content_parts.append(f"Chế độ màu: {img.mode}")
                    
                    # EXIF data nếu có
                    exif_data = self._extract_exif_data(img)
                    if exif_data:
                        content_parts.append("\n=== Thông tin EXIF ===")
                        for key, value in exif_data.items():
                            content_parts.append(f"{key}: {value}")
                    
                    # Phân tích màu sắc cơ bản
                    color_analysis = self._analyze_colors(file_path)
                    if color_analysis:
                        content_parts.append("\n=== Phân tích màu sắc ===")
                        content_parts.append(color_analysis)
                    
                    # Phát hiện đối tượng cơ bản (nếu có thể)
                    object_detection = self._basic_object_detection(file_path)
                    if object_detection:
                        content_parts.append("\n=== Phân tích nội dung ===")
                        content_parts.append(object_detection)
                
                return "\n".join(content_parts)
            
            # Chạy trong thread pool
            content = await asyncio.to_thread(extract)
            
            if not content or not content.strip():
                return "Không thể trích xuất thông tin từ hình ảnh này."
            
            return content
            
        except Exception as e:
            logger.error(f"Error extracting content from image {file_path}: {str(e)}")
            raise e

    def _extract_exif_data(self, img: Image.Image) -> Dict[str, Any]:
        """Trích xuất EXIF data từ hình ảnh"""
        try:
            exif_dict = {}
            
            if hasattr(img, '_getexif'):
                exif = img._getexif()
                if exif is not None:
                    for tag_id, value in exif.items():
                        tag = ExifTags.TAGS.get(tag_id, tag_id)
                        exif_dict[tag] = value
            
            # Lọc và format các thông tin quan trọng
            important_tags = {
                'DateTime': 'Ngày chụp',
                'Make': 'Hãng camera',
                'Model': 'Model camera',
                'Software': 'Phần mềm',
                'ImageWidth': 'Chiều rộng',
                'ImageLength': 'Chiều cao',
                'Orientation': 'Hướng',
                'XResolution': 'Độ phân giải X',
                'YResolution': 'Độ phân giải Y',
                'Flash': 'Flash',
                'FocalLength': 'Tiêu cự',
                'ExposureTime': 'Thời gian phơi sáng',
                'FNumber': 'Khẩu độ',
                'ISOSpeedRatings': 'ISO'
            }
            
            filtered_exif = {}
            for eng_tag, viet_tag in important_tags.items():
                if eng_tag in exif_dict:
                    filtered_exif[viet_tag] = str(exif_dict[eng_tag])
            
            return filtered_exif
            
        except Exception as e:
            logger.warning(f"Error extracting EXIF data: {str(e)}")
            return {}

    def _analyze_colors(self, file_path: str) -> str:
        """Phân tích màu sắc chủ đạo trong hình ảnh"""
        try:
            # Đọc hình ảnh với OpenCV
            img = cv2.imread(file_path)
            if img is None:
                return ""
            
            # Chuyển từ BGR sang RGB
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Reshape để có list các pixel
            pixels = img_rgb.reshape(-1, 3)
            
            # Tính màu trung bình
            avg_color = np.mean(pixels, axis=0)
            
            # Phân loại màu chủ đạo
            dominant_color = self._classify_color(avg_color)
            
            # Tính độ sáng trung bình
            brightness = np.mean(cv2.cvtColor(img_rgb, cv2.COLOR_RGB2GRAY))
            brightness_level = "Sáng" if brightness > 127 else "Tối"
            
            return f"Màu chủ đạo: {dominant_color}\nĐộ sáng: {brightness_level} ({brightness:.1f}/255)"
            
        except Exception as e:
            logger.warning(f"Error analyzing colors: {str(e)}")
            return ""

    def _classify_color(self, rgb_color) -> str:
        """Phân loại màu dựa trên giá trị RGB"""
        r, g, b = rgb_color
        
        # Các ngưỡng để phân loại màu
        if r > 200 and g > 200 and b > 200:
            return "Trắng/Sáng"
        elif r < 50 and g < 50 and b < 50:
            return "Đen/Tối"
        elif r > g and r > b:
            if r > 150:
                return "Đỏ"
            else:
                return "Đỏ đậm"
        elif g > r and g > b:
            if g > 150:
                return "Xanh lá"
            else:
                return "Xanh lá đậm"
        elif b > r and b > g:
            if b > 150:
                return "Xanh dương"
            else:
                return "Xanh dương đậm"
        elif r > 150 and g > 150 and b < 100:
            return "Vàng"
        elif r > 150 and g < 100 and b > 150:
            return "Tím"
        elif r > 150 and g > 100 and b < 100:
            return "Cam"
        else:
            return "Xám/Hỗn hợp"

    def _basic_object_detection(self, file_path: str) -> str:
        """Phân tích cơ bản về nội dung hình ảnh"""
        try:
            # Đọc hình ảnh
            img = cv2.imread(file_path)
            if img is None:
                return ""
            
            # Chuyển sang grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Phát hiện edges
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
            
            # Phát hiện contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            analysis_parts = []
            analysis_parts.append(f"Số lượng đối tượng phát hiện: {len(contours)}")
            analysis_parts.append(f"Mật độ chi tiết: {'Cao' if edge_density > 0.1 else 'Thấp'} ({edge_density:.3f})")
            
            # Phân loại dựa trên đặc điểm
            if edge_density > 0.15:
                analysis_parts.append("Loại: Hình ảnh phức tạp, nhiều chi tiết")
            elif edge_density > 0.05:
                analysis_parts.append("Loại: Hình ảnh trung bình")
            else:
                analysis_parts.append("Loại: Hình ảnh đơn giản, ít chi tiết")
            
            return "\n".join(analysis_parts)
            
        except Exception as e:
            logger.warning(f"Error in basic object detection: {str(e)}")
            return ""

    async def validate_file(self, file_path: str) -> bool:
        """Kiểm tra file hình ảnh có hợp lệ không"""
        try:
            def validate():
                try:
                    with Image.open(file_path) as img:
                        # Thử load hình ảnh
                        img.load()
                        return True
                except:
                    return False
            
            return await asyncio.to_thread(validate)
            
        except Exception:
            return False

    async def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Lấy metadata của file hình ảnh"""
        try:
            def get_meta():
                with Image.open(file_path) as img:
                    return {
                        "format": img.format,
                        "mode": img.mode,
                        "size": img.size,
                        "width": img.size[0],
                        "height": img.size[1],
                        "has_transparency": img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                    }
            
            return await asyncio.to_thread(get_meta)
            
        except Exception as e:
            logger.error(f"Error getting image metadata: {str(e)}")
            return {"width": 0, "height": 0}
