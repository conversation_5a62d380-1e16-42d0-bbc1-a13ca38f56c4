from fastapi import APIRouter, UploadFile, File, HTTPException
from typing import List
import os
import uuid
import logging

from app.core.config import settings
from app.services.file_processor_service import FileProcessorService
from app.services.document_service import DocumentService

router = APIRouter()

# Initialize services
file_processor = FileProcessorService()
document_service = DocumentService()

logger = logging.getLogger(__name__)


@router.post("/file")
async def upload_file(file: UploadFile = File(...)):
    """Upload và xử lý một file"""
    try:
        # Kiểm tra định dạng file
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in settings.allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Định dạng file không được hỗ trợ. Các định dạng được hỗ trợ: {', '.join(settings.allowed_extensions)}"
            )
        
        # Ki<PERSON><PERSON> tra kích thước file
        file_content = await file.read()
        if len(file_content) > settings.max_file_size:
            raise HTTPException(
                status_code=400,
                detail=f"File quá lớn. Kích thước tối đa: {settings.max_file_size / (1024*1024):.1f}MB"
            )
        
        # Tạo tên file unique
        file_id = str(uuid.uuid4())
        safe_filename = f"{file_id}_{file.filename}"
        file_path = os.path.join(settings.upload_directory, safe_filename)
        
        # Lưu file
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        # Xử lý file để extract nội dung
        extracted_content = await file_processor.process_file(file_path, file_extension)
        
        # Lưu thông tin file và nội dung vào document service
        document_info = await document_service.add_document(
            file_id=file_id,
            filename=file.filename,
            file_path=file_path,
            content=extracted_content,
            file_type=file_extension
        )
        
        return {
            "file_id": file_id,
            "filename": file.filename,
            "status": "success",
            "message": "File đã được upload và xử lý thành công",
            "content_preview": extracted_content[:500] + "..." if len(extracted_content) > 500 else extracted_content
        }
        
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        # Xóa file nếu có lỗi
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"Lỗi xử lý file: {str(e)}")


@router.post("/files")
async def upload_multiple_files(files: List[UploadFile] = File(...)):
    """Upload và xử lý nhiều file cùng lúc"""
    results = []
    
    for file in files:
        try:
            result = await upload_file(file)
            results.append(result)
        except HTTPException as e:
            results.append({
                "filename": file.filename,
                "status": "error",
                "message": e.detail
            })
    
    return {"results": results}


@router.delete("/file/{file_id}")
async def delete_file(file_id: str):
    """Xóa file đã upload"""
    try:
        success = await document_service.delete_document(file_id)
        if success:
            return {"message": "File đã được xóa thành công"}
        else:
            raise HTTPException(status_code=404, detail="Không tìm thấy file")
    except Exception as e:
        logger.error(f"Error deleting file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lỗi xóa file: {str(e)}")
