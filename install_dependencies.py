#!/usr/bin/env python3
"""
Script cài đặt dependencies từng bước để tránh lỗi
"""

import subprocess
import sys
import os

def run_pip_install(packages, description):
    """Cài đặt packages với pip"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    if isinstance(packages, str):
        packages = [packages]
    
    for package in packages:
        print(f"Installing {package}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], check=True, capture_output=True, text=True)
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}")
            print(f"Error: {e.stderr}")
            return False
    
    return True

def install_from_file(filename, description):
    """Cài đặt từ requirements file"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    if not os.path.exists(filename):
        print(f"❌ File {filename} not found")
        return False
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", filename
        ], check=True, capture_output=True, text=True)
        print(f"✅ Installed from {filename} successfully")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install from {filename}")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main installation function"""
    print("🚀 FastAPI Gemini AI Agent - Dependency Installer")
    
    # Upgrade pip first
    print("\n📦 Upgrading pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=True)
        print("✅ pip upgraded successfully")
    except subprocess.CalledProcessError as e:
        print(f"⚠️ Failed to upgrade pip: {e}")
    
    # Install core dependencies step by step
    core_packages = [
        "fastapi",
        "uvicorn[standard]", 
        "python-multipart",
        "jinja2",
        "aiofiles",
        "pydantic-settings"
    ]
    
    if not run_pip_install(core_packages, "Installing core FastAPI dependencies"):
        print("❌ Failed to install core dependencies")
        return False
    
    # Install Gemini AI
    if not run_pip_install("google-generativeai", "Installing Google Gemini AI"):
        print("❌ Failed to install Gemini AI")
        return False
    
    # Install basic file processing
    basic_packages = [
        "PyPDF2",
        "python-docx", 
        "openpyxl",
        "pandas",
        "Pillow",
        "requests",
        "beautifulsoup4",
        "python-dotenv",
        "httpx"
    ]
    
    if not run_pip_install(basic_packages, "Installing basic file processing"):
        print("❌ Failed to install basic file processing")
        return False
    
    print("\n🎉 Core dependencies installed successfully!")
    print("\n📋 Next steps:")
    print("1. Test the basic installation:")
    print("   python -c \"import fastapi; print('FastAPI OK')\"")
    print("   python -c \"import google.generativeai; print('Gemini OK')\"")
    print("\n2. Install advanced features (optional):")
    print("   python install_advanced.py")
    print("\n3. Run the application:")
    print("   python run.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
