#!/usr/bin/env python3
"""
Script để kiểm tra và sửa API key
"""

import os
import requests

def check_env_file():
    """Kiểm tra file .env"""
    if not os.path.exists('.env'):
        print("❌ File .env không tồn tại")
        create_env = input("Tạo file .env? (y/n): ")
        if create_env.lower() == 'y':
            with open('.env', 'w') as f:
                f.write("GEMINI_API_KEY=your_api_key_here\n")
            print("✅ Đã tạo file .env")
        return False
    
    print("✅ File .env tồn tại")
    return True

def get_current_api_key():
    """Lấy API key hiện tại"""
    try:
        with open('.env', 'r') as f:
            content = f.read()
            for line in content.split('\n'):
                if line.startswith('GEMINI_API_KEY='):
                    api_key = line.split('=', 1)[1].strip()
                    return api_key
        return None
    except:
        return None

def test_api_key(api_key):
    """Test API key"""
    if not api_key or api_key == "your_api_key_here":
        return False, "API key chưa được cấu hình"
    
    try:
        url = f"https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key={api_key}"
        
        headers = {"Content-Type": "application/json"}
        data = {
            "contents": [{"parts": [{"text": "Hello"}]}],
            "generationConfig": {"maxOutputTokens": 10}
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            return True, "API key hoạt động tốt!"
        elif response.status_code == 400:
            error_detail = ""
            try:
                error_json = response.json()
                if 'error' in error_json:
                    error_detail = error_json['error'].get('message', '')
            except:
                pass
            return False, f"API key không hợp lệ: {error_detail}"
        elif response.status_code == 403:
            return False, "API key bị từ chối truy cập"
        else:
            return False, f"Lỗi API: {response.status_code}"
            
    except Exception as e:
        return False, f"Lỗi kết nối: {str(e)}"

def update_api_key(new_key):
    """Cập nhật API key trong .env"""
    try:
        # Read current content
        content = ""
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                content = f.read()
        
        # Update or add API key
        lines = content.split('\n')
        updated = False
        
        for i, line in enumerate(lines):
            if line.startswith('GEMINI_API_KEY='):
                lines[i] = f"GEMINI_API_KEY={new_key}"
                updated = True
                break
        
        if not updated:
            lines.append(f"GEMINI_API_KEY={new_key}")
        
        # Write back
        with open('.env', 'w') as f:
            f.write('\n'.join(lines))
        
        return True
    except Exception as e:
        print(f"❌ Lỗi cập nhật file .env: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Gemini API Key Fixer")
    print("=" * 40)
    
    # Check .env file
    if not check_env_file():
        return
    
    # Get current API key
    current_key = get_current_api_key()
    print(f"\n🔑 API key hiện tại: {current_key[:20]}..." if current_key and len(current_key) > 20 else f"🔑 API key hiện tại: {current_key}")
    
    # Test current API key
    if current_key:
        print("\n🧪 Testing API key...")
        success, message = test_api_key(current_key)
        
        if success:
            print(f"✅ {message}")
            print("\n🎉 API key hoạt động tốt! Bạn có thể chạy app:")
            print("python app_simple.py")
            return
        else:
            print(f"❌ {message}")
    
    # Ask for new API key
    print("\n📝 Cần cập nhật API key mới")
    print("🔗 Lấy API key tại: https://makersuite.google.com/app/apikey")
    
    new_key = input("\nNhập Gemini API key mới: ").strip()
    
    if not new_key:
        print("❌ Không nhập API key")
        return
    
    # Test new API key
    print("\n🧪 Testing API key mới...")
    success, message = test_api_key(new_key)
    
    if success:
        print(f"✅ {message}")
        
        # Update .env file
        if update_api_key(new_key):
            print("✅ Đã cập nhật file .env")
            print("\n🎉 Setup hoàn tất! Chạy app:")
            print("python app_simple.py")
        else:
            print("❌ Lỗi cập nhật file .env")
    else:
        print(f"❌ {message}")
        print("💡 Kiểm tra lại API key và thử lại")

if __name__ == "__main__":
    main()
