from pydub import AudioSegment
import speech_recognition as sr
import whisper
from typing import Optional, Dict, Any
import logging
import asyncio
import os
import tempfile

logger = logging.getLogger(__name__)


class AudioProcessor:
    """Processor để xử lý file audio (.mp3, .wav)"""
    
    def __init__(self):
        """Khởi tạo audio processor"""
        self.recognizer = sr.Recognizer()
        # Khởi tạo Whisper model (có thể tốn thời gian lần đầu)
        try:
            self.whisper_model = whisper.load_model("base")
            self.use_whisper = True
        except Exception as e:
            logger.warning(f"Could not load Whisper model: {str(e)}")
            self.use_whisper = False
    
    async def extract_content(self, file_path: str) -> str:
        """
        Trích xuất nội dung từ file audio (speech-to-text)
        
        Args:
            file_path: Đường dẫn đến file audio
            
        Returns:
            str: Nội dung text đã đượ<PERSON> transcribe
        """
        try:
            content_parts = []
            
            # <PERSON><PERSON><PERSON> thông tin cơ bản về file audio
            audio_info = await self._get_audio_info(file_path)
            content_parts.append("=== Thông tin file audio ===")
            for key, value in audio_info.items():
                content_parts.append(f"{key}: {value}")
            
            # Transcribe audio thành text
            transcription = await self._transcribe_audio(file_path)
            if transcription:
                content_parts.append("\n=== Nội dung transcription ===")
                content_parts.append(transcription)
            else:
                content_parts.append("\n=== Nội dung transcription ===")
                content_parts.append("Không thể transcribe nội dung audio.")
            
            return "\n".join(content_parts)
            
        except Exception as e:
            logger.error(f"Error extracting content from audio {file_path}: {str(e)}")
            raise e

    async def _get_audio_info(self, file_path: str) -> Dict[str, Any]:
        """Lấy thông tin cơ bản về file audio"""
        try:
            def get_info():
                audio = AudioSegment.from_file(file_path)
                
                return {
                    "Thời lượng": f"{len(audio) / 1000:.2f} giây",
                    "Tần số mẫu": f"{audio.frame_rate} Hz",
                    "Số kênh": audio.channels,
                    "Độ sâu bit": f"{audio.sample_width * 8} bit",
                    "Kích thước file": f"{os.path.getsize(file_path) / (1024*1024):.2f} MB"
                }
            
            return await asyncio.to_thread(get_info)
            
        except Exception as e:
            logger.error(f"Error getting audio info: {str(e)}")
            return {"Lỗi": "Không thể lấy thông tin audio"}

    async def _transcribe_audio(self, file_path: str) -> Optional[str]:
        """Transcribe audio thành text"""
        try:
            # Thử với Whisper trước (tốt hơn)
            if self.use_whisper:
                transcription = await self._transcribe_with_whisper(file_path)
                if transcription:
                    return transcription
            
            # Fallback sang speech_recognition
            logger.info("Whisper failed or not available, trying speech_recognition")
            return await self._transcribe_with_sr(file_path)
            
        except Exception as e:
            logger.error(f"Error transcribing audio: {str(e)}")
            return None

    async def _transcribe_with_whisper(self, file_path: str) -> Optional[str]:
        """Transcribe bằng Whisper"""
        try:
            def transcribe():
                result = self.whisper_model.transcribe(file_path)
                return result["text"]
            
            # Chạy trong thread pool vì Whisper có thể tốn thời gian
            transcription = await asyncio.to_thread(transcribe)
            return transcription.strip() if transcription else None
            
        except Exception as e:
            logger.error(f"Whisper transcription failed: {str(e)}")
            return None

    async def _transcribe_with_sr(self, file_path: str) -> Optional[str]:
        """Transcribe bằng speech_recognition"""
        try:
            def transcribe():
                # Convert audio to WAV format if needed
                audio = AudioSegment.from_file(file_path)
                
                # Create temporary WAV file
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_wav:
                    audio.export(temp_wav.name, format="wav")
                    temp_wav_path = temp_wav.name
                
                try:
                    # Use speech_recognition
                    with sr.AudioFile(temp_wav_path) as source:
                        audio_data = self.recognizer.record(source)
                        
                    # Try different recognition services
                    try:
                        # Google Speech Recognition (free)
                        text = self.recognizer.recognize_google(audio_data, language='vi-VN')
                        return text
                    except sr.UnknownValueError:
                        # Try English if Vietnamese fails
                        text = self.recognizer.recognize_google(audio_data, language='en-US')
                        return text
                    except sr.RequestError:
                        # Try offline recognition if online fails
                        try:
                            text = self.recognizer.recognize_sphinx(audio_data)
                            return text
                        except:
                            return None
                
                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_wav_path):
                        os.unlink(temp_wav_path)
            
            return await asyncio.to_thread(transcribe)

        except Exception as e:
            logger.error(f"Speech recognition transcription failed: {str(e)}")
            return None

    async def validate_file(self, file_path: str) -> bool:
        """Kiểm tra file audio có hợp lệ không"""
        try:
            def validate():
                try:
                    audio = AudioSegment.from_file(file_path)
                    # Kiểm tra có thời lượng > 0
                    return len(audio) > 0
                except:
                    return False

            return await asyncio.to_thread(validate)

        except Exception:
            return False

    async def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Lấy metadata của file audio"""
        try:
            def get_meta():
                audio = AudioSegment.from_file(file_path)

                return {
                    "duration_seconds": len(audio) / 1000,
                    "frame_rate": audio.frame_rate,
                    "channels": audio.channels,
                    "sample_width": audio.sample_width,
                    "bit_depth": audio.sample_width * 8,
                    "file_size_mb": os.path.getsize(file_path) / (1024*1024)
                }

            return await asyncio.to_thread(get_meta)

        except Exception as e:
            logger.error(f"Error getting audio metadata: {str(e)}")
            return {"duration_seconds": 0}

    async def extract_audio_features(self, file_path: str) -> Dict[str, Any]:
        """Trích xuất các đặc trưng cơ bản của audio"""
        try:
            def extract_features():
                audio = AudioSegment.from_file(file_path)

                # Convert to numpy array for analysis
                samples = audio.get_array_of_samples()
                if audio.channels == 2:
                    samples = samples[0::2]  # Take left channel only

                import numpy as np
                samples_np = np.array(samples, dtype=np.float32)

                # Basic audio features
                features = {
                    "max_amplitude": float(np.max(np.abs(samples_np))),
                    "rms_energy": float(np.sqrt(np.mean(samples_np**2))),
                    "zero_crossing_rate": float(np.mean(np.abs(np.diff(np.sign(samples_np))) > 0)),
                    "duration": len(audio) / 1000,
                    "silence_ratio": float(np.sum(np.abs(samples_np) < 0.01 * np.max(np.abs(samples_np))) / len(samples_np))
                }

                return features

            return await asyncio.to_thread(extract_features)

        except Exception as e:
            logger.error(f"Error extracting audio features: {str(e)}")
            return {}
