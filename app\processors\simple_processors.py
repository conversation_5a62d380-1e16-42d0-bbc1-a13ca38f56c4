"""
Simplified processors that work with minimal dependencies
"""

import logging
import asyncio
import os
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class SimplePDFProcessor:
    """Simple PDF processor using only PyPDF2"""
    
    async def extract_content(self, file_path: str) -> str:
        try:
            import PyPDF2
            
            def extract():
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    text_content = []
                    
                    for page_num, page in enumerate(pdf_reader.pages, 1):
                        try:
                            text = page.extract_text()
                            if text:
                                text_content.append(f"--- Trang {page_num} ---\n{text}\n")
                        except Exception as e:
                            logger.warning(f"Error extracting page {page_num}: {str(e)}")
                            continue
                    
                    return "\n".join(text_content)
            
            content = await asyncio.to_thread(extract)
            return content if content else "Không thể trích xuất nội dung từ file PDF này."
            
        except ImportError:
            return "PyPDF2 không được cài đặt. Vui lòng cài đặt: pip install PyPDF2"
        except Exception as e:
            logger.error(f"Error extracting PDF content: {str(e)}")
            raise e

    async def validate_file(self, file_path: str) -> bool:
        try:
            import PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                return len(pdf_reader.pages) > 0
        except:
            return False


class SimpleWordProcessor:
    """Simple Word processor"""
    
    async def extract_content(self, file_path: str) -> str:
        try:
            from docx import Document
            
            def extract():
                doc = Document(file_path)
                content_parts = []
                
                for para in doc.paragraphs:
                    if para.text.strip():
                        content_parts.append(para.text)
                
                for table_num, table in enumerate(doc.tables, 1):
                    content_parts.append(f"\n--- Bảng {table_num} ---")
                    for row in table.rows:
                        row_text = []
                        for cell in row.cells:
                            cell_text = cell.text.strip()
                            row_text.append(cell_text if cell_text else "")
                        if any(row_text):
                            content_parts.append(" | ".join(row_text))
                
                return "\n".join(content_parts)
            
            content = await asyncio.to_thread(extract)
            return content if content else "Không thể trích xuất nội dung từ file Word này."
            
        except ImportError:
            return "python-docx không được cài đặt. Vui lòng cài đặt: pip install python-docx"
        except Exception as e:
            logger.error(f"Error extracting Word content: {str(e)}")
            raise e

    async def validate_file(self, file_path: str) -> bool:
        try:
            from docx import Document
            doc = Document(file_path)
            return True
        except:
            return False


class SimpleExcelProcessor:
    """Simple Excel processor"""
    
    async def extract_content(self, file_path: str) -> str:
        try:
            import pandas as pd
            
            def extract():
                content_parts = []
                excel_file = pd.ExcelFile(file_path)
                
                for sheet_name in excel_file.sheet_names:
                    try:
                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                        if df.empty:
                            continue
                        
                        content_parts.append(f"\n=== Sheet: {sheet_name} ===")
                        content_parts.append(f"Kích thước: {df.shape[0]} hàng x {df.shape[1]} cột")
                        
                        if not df.columns.empty:
                            content_parts.append(f"Các cột: {', '.join(str(col) for col in df.columns)}")
                        
                        # Hiển thị tối đa 50 hàng đầu
                        display_df = df.head(50)
                        df_string = display_df.to_string(index=False, max_rows=50)
                        content_parts.append(df_string)
                        
                        if len(df) > 50:
                            content_parts.append(f"\n... và {len(df) - 50} hàng khác")
                        
                    except Exception as e:
                        logger.warning(f"Error processing sheet {sheet_name}: {str(e)}")
                        continue
                
                return "\n".join(content_parts)
            
            content = await asyncio.to_thread(extract)
            return content if content else "Không thể trích xuất nội dung từ file Excel này."
            
        except ImportError:
            return "pandas và openpyxl không được cài đặt. Vui lòng cài đặt: pip install pandas openpyxl"
        except Exception as e:
            logger.error(f"Error extracting Excel content: {str(e)}")
            raise e

    async def validate_file(self, file_path: str) -> bool:
        try:
            import pandas as pd
            excel_file = pd.ExcelFile(file_path)
            return len(excel_file.sheet_names) > 0
        except:
            return False


class SimpleImageProcessor:
    """Simple Image processor"""
    
    async def extract_content(self, file_path: str) -> str:
        try:
            from PIL import Image
            
            def extract():
                with Image.open(file_path) as img:
                    content_parts = []
                    content_parts.append(f"=== Thông tin hình ảnh ===")
                    content_parts.append(f"Kích thước: {img.size[0]} x {img.size[1]} pixels")
                    content_parts.append(f"Định dạng: {img.format}")
                    content_parts.append(f"Chế độ màu: {img.mode}")
                    
                    # Thông tin file
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    content_parts.append(f"Kích thước file: {file_size:.1f} KB")
                    
                    return "\n".join(content_parts)
            
            content = await asyncio.to_thread(extract)
            return content
            
        except ImportError:
            return "Pillow không được cài đặt. Vui lòng cài đặt: pip install Pillow"
        except Exception as e:
            logger.error(f"Error extracting image info: {str(e)}")
            raise e

    async def validate_file(self, file_path: str) -> bool:
        try:
            from PIL import Image
            with Image.open(file_path) as img:
                img.load()
                return True
        except:
            return False


class SimpleAudioProcessor:
    """Simple Audio processor (basic info only)"""
    
    async def extract_content(self, file_path: str) -> str:
        try:
            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
            content_parts = []
            content_parts.append("=== Thông tin file audio ===")
            content_parts.append(f"Kích thước file: {file_size:.2f} MB")
            content_parts.append("Chức năng transcription cần cài đặt thêm dependencies:")
            content_parts.append("pip install pydub SpeechRecognition")
            
            return "\n".join(content_parts)
            
        except Exception as e:
            logger.error(f"Error getting audio info: {str(e)}")
            return "Không thể lấy thông tin file audio."

    async def validate_file(self, file_path: str) -> bool:
        return os.path.exists(file_path) and os.path.getsize(file_path) > 0


class SimpleVideoProcessor:
    """Simple Video processor (basic info only)"""
    
    async def extract_content(self, file_path: str) -> str:
        try:
            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
            content_parts = []
            content_parts.append("=== Thông tin file video ===")
            content_parts.append(f"Kích thước file: {file_size:.2f} MB")
            content_parts.append("Chức năng phân tích video cần cài đặt thêm dependencies:")
            content_parts.append("pip install moviepy opencv-python")
            
            return "\n".join(content_parts)
            
        except Exception as e:
            logger.error(f"Error getting video info: {str(e)}")
            return "Không thể lấy thông tin file video."

    async def validate_file(self, file_path: str) -> bool:
        return os.path.exists(file_path) and os.path.getsize(file_path) > 0
