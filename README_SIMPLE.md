# 🚀 FastAPI Gemini AI Agent - Simple Version

Phiên bản đơn giản chỉ cần 6 dependencies cơ bản để chạy ngay!

## ⚡ Quick Start (30 giây)

```bash
# Bước 1: Chạy script tự động
python quick_start.py

# Bước 2: Thêm API key vào file .env
# GEMINI_API_KEY=your_api_key_here

# Bước 3: Chạy app
python app_simple.py
```

Mở trình duyệt: `http://localhost:8000`

## 📦 Dependencies tối thiểu

Chỉ cần 6 packages:
```
fastapi
uvicorn
python-multipart
jinja2
python-dotenv
requests
```

## ✨ Tính năng có sẵn

- ✅ **Upload file** (text files hoạt động tốt nhất)
- ✅ **Chat với Gemini AI**
- ✅ **Tìm kiếm trong documents**
- ✅ **Giao diện web đơn giản**
- ✅ **Không cần database phức tạp**

## 🔧 Cài đặt thủ công

Nếu script tự động không hoạt động:

```bash
# Cài dependencies
pip install fastapi uvicorn python-multipart jinja2 python-dotenv requests

# Tạo file .env
echo "GEMINI_API_KEY=your_api_key_here" > .env

# Chạy app
python app_simple.py
```

## 📁 Cấu trúc đơn giản

```
FastAPI_Gemini_AI/
├── app_simple.py          # App chính (all-in-one)
├── quick_start.py         # Script setup tự động
├── .env                   # API key
├── uploads/               # Files upload
└── simple_storage/        # Document storage (JSON)
```

## 🎯 Sử dụng

1. **Upload file**: Click "Upload" và chọn file (text files tốt nhất)
2. **Chat**: Gõ câu hỏi và nhấn Enter
3. **Tìm kiếm**: AI sẽ tự động tìm trong files đã upload

## 🔍 Supported Files

- ✅ **Text files (.txt)** - Hoạt động hoàn hảo
- ⚠️ **PDF, Word, Excel** - Hiển thị thông tin cơ bản
- ⚠️ **Images, Audio, Video** - Hiển thị metadata

## 🚨 Troubleshooting

### Lỗi cài đặt?
```bash
# Upgrade pip trước
python -m pip install --upgrade pip

# Cài từng package
pip install fastapi
pip install uvicorn
pip install python-multipart
pip install jinja2
pip install python-dotenv
pip install requests
```

### Lỗi Gemini API?
- Kiểm tra API key trong file `.env`
- Đảm bảo có kết nối internet
- Thử chat với câu đơn giản: "Hello"

### Port 8000 bị chiếm?
Sửa trong `app_simple.py`:
```python
uvicorn.run(app, host="0.0.0.0", port=8001)  # Đổi port
```

## 🎉 Nâng cấp sau

Khi muốn thêm tính năng:

1. **PDF processing**: `pip install PyPDF2`
2. **Word processing**: `pip install python-docx`
3. **Excel processing**: `pip install pandas openpyxl`
4. **Image processing**: `pip install Pillow`
5. **Vector search**: `pip install chromadb sentence-transformers`

## 💡 Tips

- **Text files** hoạt động tốt nhất
- **Gemini API key** cần thiết cho chat
- **Không cần database** - dùng JSON files
- **Restart app** sau khi thay đổi .env

---

**🎯 Mục tiêu**: Chạy được ngay với dependencies tối thiểu!

**📞 Cần hỗ trợ?** Kiểm tra logs trong terminal khi chạy app.
