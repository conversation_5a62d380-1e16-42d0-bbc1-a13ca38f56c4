version: '3.8'

services:
  ai-agent:
    build: .
    container_name: fastapi-gemini-ai-agent
    ports:
      - "8000:8000"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - DATABASE_URL=sqlite:///./ai_agent.db
      - CHROMA_PERSIST_DIRECTORY=./chroma_db
      - UPLOAD_DIRECTORY=uploads
      - DEBUG=False
    volumes:
      - ./uploads:/app/uploads
      - ./chroma_db:/app/chroma_db
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: ai-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Optional: Add PostgreSQL for production database
  postgres:
    image: postgres:15-alpine
    container_name: ai-agent-postgres
    environment:
      - POSTGRES_DB=ai_agent
      - POSTGRES_USER=ai_agent
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-changeme}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Optional: Add Nginx as reverse proxy
  nginx:
    image: nginx:alpine
    container_name: ai-agent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ai-agent
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
