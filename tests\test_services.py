import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import tempfile
import os

from app.services.file_processor_service import FileProcessorService
from app.processors.pdf_processor import PDFProcessor


class TestFileProcessorService:
    """Test FileProcessorService"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.service = FileProcessorService()
    
    def test_get_supported_extensions(self):
        """Test getting supported file extensions"""
        extensions = self.service.get_supported_extensions()
        
        assert '.pdf' in extensions
        assert '.docx' in extensions
        assert '.xlsx' in extensions
        assert '.jpg' in extensions
        assert '.mp3' in extensions
        assert '.mp4' in extensions
    
    @pytest.mark.asyncio
    async def test_validate_file_nonexistent(self):
        """Test validating non-existent file"""
        result = await self.service.validate_file("nonexistent.pdf", ".pdf")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_file_unsupported_extension(self):
        """Test validating file with unsupported extension"""
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp_file:
            tmp_file.write(b"test content")
            tmp_file_path = tmp_file.name
        
        try:
            result = await self.service.validate_file(tmp_file_path, ".txt")
            assert result is False
        finally:
            os.unlink(tmp_file_path)
    
    @pytest.mark.asyncio
    async def test_get_file_info(self):
        """Test getting file information"""
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_file.write(b"fake pdf content")
            tmp_file_path = tmp_file.name
        
        try:
            info = await self.service.get_file_info(tmp_file_path)
            
            assert info["file_path"] == tmp_file_path
            assert info["file_extension"] == ".pdf"
            assert info["file_size"] > 0
            assert info["is_supported"] is True
        finally:
            os.unlink(tmp_file_path)
    
    @pytest.mark.asyncio
    @patch('app.processors.pdf_processor.PDFProcessor.extract_content')
    async def test_process_file_success(self, mock_extract):
        """Test successful file processing"""
        mock_extract.return_value = "Extracted content"
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_file.write(b"fake pdf content")
            tmp_file_path = tmp_file.name
        
        try:
            content = await self.service.process_file(tmp_file_path, ".pdf")
            assert content == "Extracted content"
            mock_extract.assert_called_once_with(tmp_file_path)
        finally:
            os.unlink(tmp_file_path)
    
    @pytest.mark.asyncio
    async def test_process_file_unsupported_extension(self):
        """Test processing file with unsupported extension"""
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp_file:
            tmp_file.write(b"test content")
            tmp_file_path = tmp_file.name
        
        try:
            with pytest.raises(ValueError, match="Không hỗ trợ định dạng file"):
                await self.service.process_file(tmp_file_path, ".txt")
        finally:
            os.unlink(tmp_file_path)


class TestPDFProcessor:
    """Test PDFProcessor"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.processor = PDFProcessor()
    
    @pytest.mark.asyncio
    async def test_validate_file_nonexistent(self):
        """Test validating non-existent PDF file"""
        result = await self.processor.validate_file("nonexistent.pdf")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_extract_content_nonexistent_file(self):
        """Test extracting content from non-existent file"""
        with pytest.raises(FileNotFoundError):
            await self.processor.extract_content("nonexistent.pdf")
    
    @pytest.mark.asyncio
    @patch('PyPDF2.PdfReader')
    async def test_extract_with_pypdf2_success(self, mock_pdf_reader):
        """Test successful extraction with PyPDF2"""
        # Mock PDF reader
        mock_page = MagicMock()
        mock_page.extract_text.return_value = "Test content from page 1"
        
        mock_reader_instance = MagicMock()
        mock_reader_instance.pages = [mock_page]
        mock_pdf_reader.return_value = mock_reader_instance
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_file.write(b"fake pdf content")
            tmp_file_path = tmp_file.name
        
        try:
            # Mock pdfplumber to fail so it falls back to PyPDF2
            with patch('pdfplumber.open', side_effect=Exception("pdfplumber failed")):
                content = await self.processor.extract_content(tmp_file_path)
                assert "Test content from page 1" in content
                assert "--- Trang 1 ---" in content
        finally:
            os.unlink(tmp_file_path)


class TestGeminiService:
    """Test GeminiService (mocked)"""
    
    @pytest.mark.asyncio
    @patch('google.generativeai.configure')
    @patch('google.generativeai.GenerativeModel')
    async def test_generate_response_success(self, mock_model_class, mock_configure):
        """Test successful response generation"""
        # Mock the model and response
        mock_response = MagicMock()
        mock_candidate = MagicMock()
        mock_part = MagicMock()
        mock_part.text = "This is a test response"
        mock_candidate.content.parts = [mock_part]
        mock_response.candidates = [mock_candidate]
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model_instance
        
        # Import and test
        from app.services.gemini_service import GeminiService
        service = GeminiService()
        
        response = await service.generate_response("Test prompt")
        assert response == "This is a test response"
    
    @pytest.mark.asyncio
    @patch('google.generativeai.configure')
    @patch('google.generativeai.GenerativeModel')
    async def test_analyze_document_content(self, mock_model_class, mock_configure):
        """Test document content analysis"""
        # Mock the model response with JSON
        mock_response = MagicMock()
        mock_candidate = MagicMock()
        mock_part = MagicMock()
        mock_part.text = '{"short_summary": "Test summary", "keywords": ["test", "document"], "main_topic": "Testing"}'
        mock_candidate.content.parts = [mock_part]
        mock_response.candidates = [mock_candidate]
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model_instance
        
        from app.services.gemini_service import GeminiService
        service = GeminiService()
        
        analysis = await service.analyze_document_content("Test content", ".pdf")
        
        assert "short_summary" in analysis
        assert "keywords" in analysis
        assert "main_topic" in analysis


class TestWebSearchService:
    """Test WebSearchService"""
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_search_with_scraping_success(self, mock_client_class):
        """Test successful web search with scraping"""
        # Mock HTML response
        mock_html = """
        <html>
            <body>
                <div class="g">
                    <h3>Test Title</h3>
                    <a href="https://example.com">Test Link</a>
                    <span class="aCOpRe">Test snippet</span>
                </div>
            </body>
        </html>
        """
        
        mock_response = MagicMock()
        mock_response.text = mock_html
        mock_response.raise_for_status.return_value = None
        
        mock_client_instance = MagicMock()
        mock_client_instance.get = AsyncMock(return_value=mock_response)
        mock_client_instance.__aenter__ = AsyncMock(return_value=mock_client_instance)
        mock_client_instance.__aexit__ = AsyncMock(return_value=None)
        
        mock_client_class.return_value = mock_client_instance
        
        from app.services.web_search_service import WebSearchService
        service = WebSearchService()
        service.use_google_api = False  # Force scraping
        
        # Mock the page content fetching to avoid additional HTTP calls
        with patch.object(service, '_fetch_page_contents', return_value=[{
            'title': 'Test Title',
            'url': 'https://example.com',
            'snippet': 'Test snippet',
            'content': 'Test content',
            'source': 'Google Scraping'
        }]):
            results = await service.search("test query")
            
            assert "results" in results
            assert len(results["results"]) > 0
            assert results["results"][0]["title"] == "Test Title"


if __name__ == "__main__":
    pytest.main([__file__])
