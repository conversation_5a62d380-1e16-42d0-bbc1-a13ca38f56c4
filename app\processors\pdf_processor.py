import PyPDF2
import pdfplumber
from typing import Optional
import logging
import asyncio

logger = logging.getLogger(__name__)


class PDFProcessor:
    """Processor để xử lý file PDF"""
    
    async def extract_content(self, file_path: str) -> str:
        """
        Trích xuất nội dung text từ file PDF
        
        Args:
            file_path: Đường dẫn đến file PDF
            
        Returns:
            str: Nội dung text đã trích xuất
        """
        try:
            # Thử với pdfplumber trước (tốt hơn cho layout phức tạp)
            content = await self._extract_with_pdfplumber(file_path)
            
            if content and content.strip():
                return content
            
            # Fallback sang PyPDF2
            logger.info("pdfplumber failed, trying PyPDF2")
            content = await self._extract_with_pypdf2(file_path)
            
            return content if content else "Không thể trích xuất nội dung từ file PDF này."
            
        except Exception as e:
            logger.error(f"Error extracting content from PDF {file_path}: {str(e)}")
            raise e

    async def _extract_with_pdfplumber(self, file_path: str) -> Optional[str]:
        """Trích xuất nội dung bằng pdfplumber"""
        try:
            def extract():
                with pdfplumber.open(file_path) as pdf:
                    text_content = []
                    
                    for page_num, page in enumerate(pdf.pages, 1):
                        try:
                            # Trích xuất text
                            text = page.extract_text()
                            if text:
                                text_content.append(f"--- Trang {page_num} ---\n{text}\n")
                            
                            # Trích xuất tables nếu có
                            tables = page.extract_tables()
                            if tables:
                                for table_num, table in enumerate(tables, 1):
                                    text_content.append(f"\n--- Bảng {table_num} (Trang {page_num}) ---\n")
                                    for row in table:
                                        if row:
                                            row_text = " | ".join([str(cell) if cell else "" for cell in row])
                                            text_content.append(row_text + "\n")
                                    text_content.append("\n")
                                    
                        except Exception as e:
                            logger.warning(f"Error extracting page {page_num}: {str(e)}")
                            continue
                    
                    return "\n".join(text_content)
            
            # Chạy trong thread pool để tránh block
            content = await asyncio.to_thread(extract)
            return content
            
        except Exception as e:
            logger.error(f"pdfplumber extraction failed: {str(e)}")
            return None

    async def _extract_with_pypdf2(self, file_path: str) -> Optional[str]:
        """Trích xuất nội dung bằng PyPDF2"""
        try:
            def extract():
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    text_content = []
                    
                    for page_num, page in enumerate(pdf_reader.pages, 1):
                        try:
                            text = page.extract_text()
                            if text:
                                text_content.append(f"--- Trang {page_num} ---\n{text}\n")
                        except Exception as e:
                            logger.warning(f"Error extracting page {page_num} with PyPDF2: {str(e)}")
                            continue
                    
                    return "\n".join(text_content)
            
            # Chạy trong thread pool
            content = await asyncio.to_thread(extract)
            return content
            
        except Exception as e:
            logger.error(f"PyPDF2 extraction failed: {str(e)}")
            return None

    async def validate_file(self, file_path: str) -> bool:
        """Kiểm tra file PDF có hợp lệ không"""
        try:
            def validate():
                with open(file_path, 'rb') as file:
                    try:
                        pdf_reader = PyPDF2.PdfReader(file)
                        # Kiểm tra có ít nhất 1 trang
                        return len(pdf_reader.pages) > 0
                    except:
                        return False
            
            return await asyncio.to_thread(validate)
            
        except Exception:
            return False

    async def get_metadata(self, file_path: str) -> dict:
        """Lấy metadata của file PDF"""
        try:
            def get_meta():
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    metadata = pdf_reader.metadata
                    
                    return {
                        "title": metadata.get("/Title", ""),
                        "author": metadata.get("/Author", ""),
                        "subject": metadata.get("/Subject", ""),
                        "creator": metadata.get("/Creator", ""),
                        "producer": metadata.get("/Producer", ""),
                        "creation_date": str(metadata.get("/CreationDate", "")),
                        "modification_date": str(metadata.get("/ModDate", "")),
                        "page_count": len(pdf_reader.pages)
                    }
            
            return await asyncio.to_thread(get_meta)
            
        except Exception as e:
            logger.error(f"Error getting PDF metadata: {str(e)}")
            return {"page_count": 0}
