from typing import Dict, Any, Optional
import logging
import os

logger = logging.getLogger(__name__)

# Try to import advanced processors, fallback to simple ones
try:
    from app.processors.pdf_processor import PDFProcessor
except ImportError:
    from app.processors.simple_processors import SimplePDFProcessor as PDFProcessor
    logger.warning("Using simple PDF processor (advanced features unavailable)")

try:
    from app.processors.word_processor import WordProcessor
except ImportError:
    from app.processors.simple_processors import SimpleWordProcessor as WordProcessor
    logger.warning("Using simple Word processor")

try:
    from app.processors.excel_processor import ExcelProcessor
except ImportError:
    from app.processors.simple_processors import SimpleExcelProcessor as ExcelProcessor
    logger.warning("Using simple Excel processor")

try:
    from app.processors.image_processor import ImageProcessor
except ImportError:
    from app.processors.simple_processors import SimpleImageProcessor as ImageProcessor
    logger.warning("Using simple Image processor (advanced features unavailable)")

try:
    from app.processors.audio_processor import AudioProcessor
except ImportError:
    from app.processors.simple_processors import SimpleAudioProcessor as AudioProcessor
    logger.warning("Using simple Audio processor (transcription unavailable)")

try:
    from app.processors.video_processor import VideoProcessor
except ImportError:
    from app.processors.simple_processors import SimpleVideoProcessor as VideoProcessor
    logger.warning("Using simple Video processor (analysis unavailable)")


class FileProcessorService:
    """Service chính để xử lý các loại file khác nhau"""
    
    def __init__(self):
        """Khởi tạo các processor cho từng loại file"""
        self.processors = {
            '.pdf': PDFProcessor(),
            '.docx': WordProcessor(),
            '.doc': WordProcessor(),
            '.xlsx': ExcelProcessor(),
            '.xls': ExcelProcessor(),
            '.jpg': ImageProcessor(),
            '.jpeg': ImageProcessor(),
            '.png': ImageProcessor(),
            '.gif': ImageProcessor(),
            '.mp3': AudioProcessor(),
            '.wav': AudioProcessor(),
            '.mp4': VideoProcessor(),
            '.avi': VideoProcessor(),
            '.mov': VideoProcessor(),
        }
        
        logger.info("File processor service initialized")

    async def process_file(self, file_path: str, file_extension: str) -> str:
        """
        Xử lý file và extract nội dung
        
        Args:
            file_path: Đường dẫn đến file
            file_extension: Phần mở rộng của file (.pdf, .docx, etc.)
            
        Returns:
            str: Nội dung đã được extract từ file
        """
        try:
            # Kiểm tra file có tồn tại không
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File không tồn tại: {file_path}")
            
            # Lấy processor phù hợp
            processor = self.processors.get(file_extension.lower())
            if not processor:
                raise ValueError(f"Không hỗ trợ định dạng file: {file_extension}")
            
            # Xử lý file
            logger.info(f"Processing file: {file_path} with extension: {file_extension}")
            content = await processor.extract_content(file_path)
            
            if not content or not content.strip():
                logger.warning(f"No content extracted from file: {file_path}")
                return "Không thể trích xuất nội dung từ file này."
            
            logger.info(f"Successfully extracted {len(content)} characters from {file_path}")
            return content
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
            raise e

    async def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        Lấy thông tin cơ bản về file
        
        Args:
            file_path: Đường dẫn đến file
            
        Returns:
            Dict chứa thông tin file
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File không tồn tại: {file_path}")
            
            stat = os.stat(file_path)
            file_extension = os.path.splitext(file_path)[1].lower()
            
            return {
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "file_extension": file_extension,
                "file_size": stat.st_size,
                "created_time": stat.st_ctime,
                "modified_time": stat.st_mtime,
                "is_supported": file_extension in self.processors
            }
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {str(e)}")
            raise e

    def get_supported_extensions(self) -> list:
        """Lấy danh sách các định dạng file được hỗ trợ"""
        return list(self.processors.keys())

    async def validate_file(self, file_path: str, file_extension: str) -> bool:
        """
        Kiểm tra file có hợp lệ và có thể xử lý được không
        
        Args:
            file_path: Đường dẫn đến file
            file_extension: Phần mở rộng của file
            
        Returns:
            bool: True nếu file hợp lệ
        """
        try:
            # Kiểm tra file tồn tại
            if not os.path.exists(file_path):
                return False
            
            # Kiểm tra định dạng được hỗ trợ
            if file_extension.lower() not in self.processors:
                return False
            
            # Kiểm tra file không rỗng
            if os.path.getsize(file_path) == 0:
                return False
            
            # Có thể thêm các kiểm tra khác tùy theo loại file
            processor = self.processors[file_extension.lower()]
            if hasattr(processor, 'validate_file'):
                return await processor.validate_file(file_path)
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating file {file_path}: {str(e)}")
            return False
