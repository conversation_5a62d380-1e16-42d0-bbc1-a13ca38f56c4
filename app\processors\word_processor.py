from docx import Document
from typing import Optional
import logging
import asyncio

logger = logging.getLogger(__name__)


class WordProcessor:
    """Processor để xử lý file Word (.docx, .doc)"""
    
    async def extract_content(self, file_path: str) -> str:
        """
        Trích xuất nội dung text từ file Word
        
        Args:
            file_path: Đường dẫn đến file Word
            
        Returns:
            str: Nội dung text đã trích xuất
        """
        try:
            def extract():
                doc = Document(file_path)
                content_parts = []
                
                # Trích xuất paragraphs
                for para in doc.paragraphs:
                    if para.text.strip():
                        content_parts.append(para.text)
                
                # Trích xuất tables
                for table_num, table in enumerate(doc.tables, 1):
                    content_parts.append(f"\n--- Bảng {table_num} ---")
                    
                    for row in table.rows:
                        row_text = []
                        for cell in row.cells:
                            cell_text = cell.text.strip()
                            row_text.append(cell_text if cell_text else "")
                        
                        if any(row_text):  # Chỉ thêm row nếu có nội dung
                            content_parts.append(" | ".join(row_text))
                    
                    content_parts.append("")  # Dòng trống sau table
                
                return "\n".join(content_parts)
            
            # Chạy trong thread pool để tránh block
            content = await asyncio.to_thread(extract)
            
            if not content or not content.strip():
                return "Không thể trích xuất nội dung từ file Word này."
            
            return content
            
        except Exception as e:
            logger.error(f"Error extracting content from Word file {file_path}: {str(e)}")
            raise e

    async def validate_file(self, file_path: str) -> bool:
        """Kiểm tra file Word có hợp lệ không"""
        try:
            def validate():
                try:
                    doc = Document(file_path)
                    # Kiểm tra có thể mở được file
                    return True
                except:
                    return False
            
            return await asyncio.to_thread(validate)
            
        except Exception:
            return False

    async def get_metadata(self, file_path: str) -> dict:
        """Lấy metadata của file Word"""
        try:
            def get_meta():
                doc = Document(file_path)
                core_props = doc.core_properties
                
                return {
                    "title": core_props.title or "",
                    "author": core_props.author or "",
                    "subject": core_props.subject or "",
                    "keywords": core_props.keywords or "",
                    "comments": core_props.comments or "",
                    "created": str(core_props.created) if core_props.created else "",
                    "modified": str(core_props.modified) if core_props.modified else "",
                    "last_modified_by": core_props.last_modified_by or "",
                    "paragraph_count": len(doc.paragraphs),
                    "table_count": len(doc.tables)
                }
            
            return await asyncio.to_thread(get_meta)
            
        except Exception as e:
            logger.error(f"Error getting Word metadata: {str(e)}")
            return {"paragraph_count": 0, "table_count": 0}

    async def extract_images_info(self, file_path: str) -> list:
        """Trích xuất thông tin về hình ảnh trong document"""
        try:
            def extract_images():
                doc = Document(file_path)
                images_info = []
                
                # Lấy thông tin về các hình ảnh
                for rel in doc.part.rels.values():
                    if "image" in rel.target_ref:
                        images_info.append({
                            "image_id": rel.rId,
                            "target": rel.target_ref,
                            "type": rel.reltype
                        })
                
                return images_info
            
            return await asyncio.to_thread(extract_images)
            
        except Exception as e:
            logger.error(f"Error extracting images info: {str(e)}")
            return []
