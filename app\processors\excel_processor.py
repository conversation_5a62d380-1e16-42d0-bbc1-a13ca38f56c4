import pandas as pd
from openpyxl import load_workbook
from typing import Optional, Dict, Any
import logging
import asyncio

logger = logging.getLogger(__name__)


class ExcelProcessor:
    """Processor để xử lý file Excel (.xlsx, .xls)"""
    
    async def extract_content(self, file_path: str) -> str:
        """
        Trích xuất nội dung từ file Excel
        
        Args:
            file_path: Đường dẫn đến file Excel
            
        Returns:
            str: Nội dung đã được format thành text
        """
        try:
            def extract():
                content_parts = []
                
                # Đọc tất cả sheets
                excel_file = pd.ExcelFile(file_path)
                
                for sheet_name in excel_file.sheet_names:
                    try:
                        # Đọc sheet
                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                        
                        if df.empty:
                            continue
                        
                        content_parts.append(f"\n=== Sheet: {sheet_name} ===")
                        
                        # Thông tin cơ bản về sheet
                        content_parts.append(f"Kích thước: {df.shape[0]} hàng x {df.shape[1]} cột")
                        
                        # Tên các cột
                        if not df.columns.empty:
                            content_parts.append(f"Các cột: {', '.join(str(col) for col in df.columns)}")
                        
                        # Dữ liệu (giới hạn số hàng để tránh quá dài)
                        max_rows = 100  # Giới hạn 100 hàng đầu
                        display_df = df.head(max_rows)
                        
                        # Convert DataFrame to string với format đẹp
                        df_string = display_df.to_string(index=False, max_rows=max_rows)
                        content_parts.append(df_string)
                        
                        if len(df) > max_rows:
                            content_parts.append(f"\n... và {len(df) - max_rows} hàng khác")
                        
                        # Thống kê cơ bản cho các cột số
                        numeric_cols = df.select_dtypes(include=['number']).columns
                        if len(numeric_cols) > 0:
                            content_parts.append("\n--- Thống kê cơ bản ---")
                            stats = df[numeric_cols].describe()
                            content_parts.append(stats.to_string())
                        
                        content_parts.append("\n")
                        
                    except Exception as e:
                        logger.warning(f"Error processing sheet {sheet_name}: {str(e)}")
                        content_parts.append(f"Lỗi xử lý sheet {sheet_name}: {str(e)}")
                        continue
                
                return "\n".join(content_parts)
            
            # Chạy trong thread pool
            content = await asyncio.to_thread(extract)
            
            if not content or not content.strip():
                return "Không thể trích xuất nội dung từ file Excel này."
            
            return content
            
        except Exception as e:
            logger.error(f"Error extracting content from Excel file {file_path}: {str(e)}")
            raise e

    async def validate_file(self, file_path: str) -> bool:
        """Kiểm tra file Excel có hợp lệ không"""
        try:
            def validate():
                try:
                    # Thử đọc file với pandas
                    excel_file = pd.ExcelFile(file_path)
                    return len(excel_file.sheet_names) > 0
                except:
                    return False
            
            return await asyncio.to_thread(validate)
            
        except Exception:
            return False

    async def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Lấy metadata của file Excel"""
        try:
            def get_meta():
                metadata = {
                    "sheet_names": [],
                    "total_sheets": 0,
                    "total_rows": 0,
                    "total_columns": 0,
                    "sheet_details": []
                }
                
                try:
                    # Sử dụng pandas để lấy thông tin cơ bản
                    excel_file = pd.ExcelFile(file_path)
                    metadata["sheet_names"] = excel_file.sheet_names
                    metadata["total_sheets"] = len(excel_file.sheet_names)
                    
                    for sheet_name in excel_file.sheet_names:
                        try:
                            df = pd.read_excel(file_path, sheet_name=sheet_name)
                            sheet_info = {
                                "name": sheet_name,
                                "rows": len(df),
                                "columns": len(df.columns),
                                "column_names": list(df.columns)
                            }
                            metadata["sheet_details"].append(sheet_info)
                            metadata["total_rows"] += len(df)
                            metadata["total_columns"] += len(df.columns)
                        except Exception as e:
                            logger.warning(f"Error getting metadata for sheet {sheet_name}: {str(e)}")
                    
                    # Thử lấy thêm metadata từ openpyxl (chỉ cho .xlsx)
                    if file_path.endswith('.xlsx'):
                        try:
                            wb = load_workbook(file_path, read_only=True)
                            metadata["workbook_properties"] = {
                                "creator": wb.properties.creator or "",
                                "title": wb.properties.title or "",
                                "description": wb.properties.description or "",
                                "created": str(wb.properties.created) if wb.properties.created else "",
                                "modified": str(wb.properties.modified) if wb.properties.modified else ""
                            }
                        except Exception as e:
                            logger.warning(f"Error getting workbook properties: {str(e)}")
                
                except Exception as e:
                    logger.error(f"Error getting Excel metadata: {str(e)}")
                
                return metadata
            
            return await asyncio.to_thread(get_meta)
            
        except Exception as e:
            logger.error(f"Error getting Excel metadata: {str(e)}")
            return {"total_sheets": 0, "total_rows": 0, "total_columns": 0}

    async def extract_specific_sheet(self, file_path: str, sheet_name: str) -> str:
        """Trích xuất nội dung từ một sheet cụ thể"""
        try:
            def extract_sheet():
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                if df.empty:
                    return f"Sheet '{sheet_name}' không có dữ liệu."
                
                content_parts = [f"=== Sheet: {sheet_name} ==="]
                content_parts.append(f"Kích thước: {df.shape[0]} hàng x {df.shape[1]} cột")
                content_parts.append(f"Các cột: {', '.join(str(col) for col in df.columns)}")
                content_parts.append(df.to_string(index=False))
                
                return "\n".join(content_parts)
            
            return await asyncio.to_thread(extract_sheet)
            
        except Exception as e:
            logger.error(f"Error extracting sheet {sheet_name}: {str(e)}")
            raise e
