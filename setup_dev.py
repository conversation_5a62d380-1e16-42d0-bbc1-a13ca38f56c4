#!/usr/bin/env python3
"""
Script setup môi trường development cho FastAPI Gemini AI Agent
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description, check=True):
    """Chạy command với description"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} - Success")
            return True
        else:
            print(f"❌ {description} - Failed")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed: {e}")
        return False

def check_python_version():
    """Kiểm tra phiên bản Python"""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is supported")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not supported. Need Python 3.8+")
        return False

def create_virtual_environment():
    """Tạo virtual environment"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("📁 Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")

def install_dependencies():
    """Cài đặt dependencies"""
    # Detect OS and use appropriate activation script
    if os.name == 'nt':  # Windows
        pip_command = "venv\\Scripts\\pip install -r requirements.txt"
    else:  # Linux/Mac
        pip_command = "source venv/bin/activate && pip install -r requirements.txt"
    
    return run_command(pip_command, "Installing dependencies")

def create_env_file():
    """Tạo file .env từ .env.example"""
    env_path = Path(".env")
    env_example_path = Path(".env.example")
    
    if env_path.exists():
        print("📄 .env file already exists")
        return True
    
    if not env_example_path.exists():
        print("❌ .env.example file not found")
        return False
    
    try:
        shutil.copy(env_example_path, env_path)
        print("✅ Created .env file from .env.example")
        print("⚠️  Please edit .env file and add your GEMINI_API_KEY")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def create_directories():
    """Tạo các thư mục cần thiết"""
    directories = [
        "uploads",
        "chroma_db", 
        "logs",
        "reports",
        "htmlcov"
    ]
    
    print("📁 Creating necessary directories...")
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"  ✅ {directory}/")
    
    return True

def check_gemini_api_key():
    """Kiểm tra Gemini API key"""
    env_path = Path(".env")
    if not env_path.exists():
        print("⚠️  .env file not found. Please create it and add GEMINI_API_KEY")
        return False
    
    try:
        with open(env_path, 'r') as f:
            content = f.read()
            if "GEMINI_API_KEY=your_gemini_api_key_here" in content:
                print("⚠️  Please update GEMINI_API_KEY in .env file")
                return False
            elif "GEMINI_API_KEY=" in content and len(content.split("GEMINI_API_KEY=")[1].split('\n')[0].strip()) > 10:
                print("✅ GEMINI_API_KEY appears to be set")
                return True
            else:
                print("⚠️  GEMINI_API_KEY not properly configured in .env file")
                return False
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def run_basic_tests():
    """Chạy basic tests để kiểm tra setup"""
    print("🧪 Running basic tests...")
    
    # Test import các module chính
    test_imports = [
        "import fastapi",
        "import uvicorn", 
        "import google.generativeai",
        "from app.main import app"
    ]
    
    for test_import in test_imports:
        try:
            if os.name == 'nt':  # Windows
                command = f"venv\\Scripts\\python -c \"{test_import}\""
            else:  # Linux/Mac
                command = f"source venv/bin/activate && python -c \"{test_import}\""
            
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
            print(f"  ✅ {test_import}")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ {test_import} - {e}")
            return False
    
    return True

def print_next_steps():
    """In hướng dẫn các bước tiếp theo"""
    print(f"\n{'='*60}")
    print("🎉 SETUP COMPLETED!")
    print(f"{'='*60}")
    print("\n📋 Next steps:")
    print("1. Edit .env file and add your GEMINI_API_KEY")
    print("2. Activate virtual environment:")
    
    if os.name == 'nt':  # Windows
        print("   venv\\Scripts\\activate")
    else:  # Linux/Mac
        print("   source venv/bin/activate")
    
    print("3. Run the application:")
    print("   python run.py")
    print("4. Open browser and go to: http://localhost:8000")
    print("5. Run tests:")
    print("   python run_tests.py")
    
    print(f"\n{'='*60}")
    print("📚 Documentation:")
    print("- README.md for detailed instructions")
    print("- API docs: http://localhost:8000/docs (when running)")
    print(f"{'='*60}")

def main():
    """Main setup function"""
    print("🚀 FastAPI Gemini AI Agent - Development Setup")
    print(f"{'='*60}")
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Creating virtual environment", create_virtual_environment),
        ("Installing dependencies", install_dependencies),
        ("Creating .env file", create_env_file),
        ("Creating directories", create_directories),
        ("Checking Gemini API key", check_gemini_api_key),
        ("Running basic tests", run_basic_tests)
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        print(f"\n📋 Step: {step_name}")
        if not step_function():
            failed_steps.append(step_name)
    
    if failed_steps:
        print(f"\n❌ Setup completed with issues:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nPlease resolve the issues above before running the application.")
    else:
        print_next_steps()

if __name__ == "__main__":
    main()
