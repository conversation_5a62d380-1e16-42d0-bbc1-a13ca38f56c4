#!/usr/bin/env python3
"""
FastAPI Gemini AI Agent - <PERSON><PERSON>n bản đơn giản
Chỉ sử dụng dependencies cơ bản nhất
"""

from fastapi import FastAPI, Request, UploadFile, File, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from typing import List, Optional
import os
import uuid
import json
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
except ImportError:
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")

# Create FastAPI app
app = FastAPI(
    title="FastAPI Gemini AI Agent - Simple",
    version="1.0.0",
    description="AI Agent đơn giản để phân tích file"
)

# Create directories
os.makedirs("uploads", exist_ok=True)
os.makedirs("static/css", exist_ok=True)
os.makedirs("static/js", exist_ok=True)
os.makedirs("templates", exist_ok=True)
os.makedirs("simple_storage", exist_ok=True)

# Mount static files
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    pass

# Setup templates
templates = Jinja2Templates(directory="templates")

# Simple storage
DOCUMENTS_FILE = "simple_storage/documents.json"

def load_documents():
    """Load documents from JSON file"""
    try:
        if os.path.exists(DOCUMENTS_FILE):
            with open(DOCUMENTS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except:
        return {}

def save_documents(documents):
    """Save documents to JSON file"""
    try:
        with open(DOCUMENTS_FILE, 'w', encoding='utf-8') as f:
            json.dump(documents, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"Error saving documents: {e}")

def extract_text_from_file(file_path: str, file_extension: str) -> str:
    """Extract text from file - very basic"""
    try:
        if file_extension.lower() == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            # For other file types, just return basic info
            file_size = os.path.getsize(file_path) / 1024  # KB
            return f"File: {os.path.basename(file_path)}\nSize: {file_size:.1f} KB\nType: {file_extension}\n\nNote: Để xử lý file {file_extension}, cần cài thêm dependencies."
    except Exception as e:
        return f"Không thể đọc file: {str(e)}"

def simple_gemini_request(prompt: str) -> str:
    """Simple Gemini API request"""
    if not GEMINI_API_KEY:
        return "Chưa cấu hình GEMINI_API_KEY. Vui lòng thêm API key vào file .env"
    
    try:
        import requests
        
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={GEMINI_API_KEY}"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }]
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                return result['candidates'][0]['content']['parts'][0]['text']
            else:
                return "Gemini không trả về kết quả hợp lệ"
        else:
            return f"Lỗi API Gemini: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Lỗi kết nối Gemini: {str(e)}"

# Pydantic models
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    sources: List[str] = []
    source_type: str = "simple"

# Routes
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Trang chủ"""
    return """
    <!DOCTYPE html>
    <html lang="vi">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI Agent - Simple</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .chat-container { border: 1px solid #ddd; height: 400px; overflow-y: auto; padding: 10px; margin: 20px 0; }
            .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
            .user { background: #e3f2fd; text-align: right; }
            .assistant { background: #f5f5f5; }
            .upload-area { border: 2px dashed #ddd; padding: 20px; text-align: center; margin: 20px 0; }
            input, button { padding: 10px; margin: 5px; }
            #messageInput { width: 70%; }
            #sendButton { width: 20%; }
        </style>
    </head>
    <body>
        <h1>🤖 AI Agent - Simple Version</h1>
        
        <div class="upload-area">
            <h3>📁 Upload File</h3>
            <input type="file" id="fileInput" accept=".txt,.pdf,.docx,.xlsx,.jpg,.png,.mp3,.mp4">
            <button onclick="uploadFile()">Upload</button>
            <div id="uploadStatus"></div>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="message assistant">
                <strong>AI:</strong> Xin chào! Tôi có thể giúp bạn phân tích file và trả lời câu hỏi. 
                Hiện tại đang chạy ở chế độ đơn giản.
            </div>
        </div>
        
        <div>
            <input type="text" id="messageInput" placeholder="Nhập câu hỏi..." onkeypress="handleKeyPress(event)">
            <button id="sendButton" onclick="sendMessage()">Gửi</button>
        </div>
        
        <script>
            async function uploadFile() {
                const fileInput = document.getElementById('fileInput');
                const file = fileInput.files[0];
                if (!file) {
                    alert('Vui lòng chọn file');
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', file);
                
                document.getElementById('uploadStatus').innerHTML = 'Đang upload...';
                
                try {
                    const response = await fetch('/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    if (response.ok) {
                        document.getElementById('uploadStatus').innerHTML = '✅ Upload thành công!';
                        addMessage('assistant', `File "${file.name}" đã được upload và xử lý.`);
                    } else {
                        document.getElementById('uploadStatus').innerHTML = '❌ ' + result.detail;
                    }
                } catch (error) {
                    document.getElementById('uploadStatus').innerHTML = '❌ Lỗi upload: ' + error.message;
                }
            }
            
            async function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (!message) return;
                
                addMessage('user', message);
                input.value = '';
                
                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message: message })
                    });
                    
                    const result = await response.json();
                    addMessage('assistant', result.response);
                } catch (error) {
                    addMessage('assistant', 'Lỗi: ' + error.message);
                }
            }
            
            function addMessage(sender, content) {
                const container = document.getElementById('chatContainer');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                messageDiv.innerHTML = `<strong>${sender === 'user' ? 'Bạn' : 'AI'}:</strong> ${content}`;
                container.appendChild(messageDiv);
                container.scrollTop = container.scrollHeight;
            }
            
            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }
        </script>
    </body>
    </html>
    """

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload file endpoint"""
    try:
        # Check file size (max 10MB)
        content = await file.read()
        if len(content) > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="File quá lớn (max 10MB)")
        
        # Save file
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1]
        safe_filename = f"{file_id}_{file.filename}"
        file_path = os.path.join("uploads", safe_filename)
        
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Extract content
        extracted_content = extract_text_from_file(file_path, file_extension)
        
        # Save to storage
        documents = load_documents()
        documents[file_id] = {
            "file_id": file_id,
            "filename": file.filename,
            "file_path": file_path,
            "file_type": file_extension,
            "content": extracted_content,
            "upload_time": datetime.now().isoformat()
        }
        save_documents(documents)
        
        return {
            "file_id": file_id,
            "filename": file.filename,
            "status": "success",
            "message": "File đã được upload thành công"
        }
        
    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat", response_model=ChatResponse)
async def chat(chat_message: ChatMessage):
    """Chat endpoint"""
    try:
        user_message = chat_message.message
        
        # Search in uploaded documents
        documents = load_documents()
        relevant_content = ""
        sources = []
        
        for doc_id, doc in documents.items():
            if user_message.lower() in doc["content"].lower():
                relevant_content += f"\nTừ file {doc['filename']}:\n{doc['content'][:500]}...\n"
                sources.append(doc["filename"])
        
        if relevant_content:
            # Use document content
            prompt = f"""
            Dựa trên thông tin từ các file đã upload:
            {relevant_content}
            
            Câu hỏi: {user_message}
            
            Hãy trả lời dựa trên thông tin có sẵn.
            """
            response = simple_gemini_request(prompt)
            
            return ChatResponse(
                response=response,
                sources=sources,
                source_type="documents"
            )
        else:
            # Direct Gemini response
            response = simple_gemini_request(user_message)
            
            return ChatResponse(
                response=response,
                sources=[],
                source_type="gemini"
            )
            
    except Exception as e:
        logger.error(f"Chat error: {e}")
        return ChatResponse(
            response=f"Lỗi xử lý: {str(e)}",
            sources=[],
            source_type="error"
        )

@app.get("/health")
async def health_check():
    """Health check"""
    return {"status": "healthy", "version": "simple"}

@app.get("/documents")
async def list_documents():
    """List uploaded documents"""
    documents = load_documents()
    return {"documents": list(documents.values())}

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Simple AI Agent...")
    print("📋 Features:")
    print("  ✅ Basic file upload")
    print("  ✅ Text file processing")
    print("  ✅ Simple chat with Gemini")
    print("  ✅ Document search")
    print("\n🌐 Open: http://localhost:8000")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
