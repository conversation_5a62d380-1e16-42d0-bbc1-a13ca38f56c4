"""
Custom middleware for FastAPI Gemini AI Agent
"""

import time
import logging
import traceback
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGI<PERSON>pp
import uuid

logger = logging.getLogger("app.middleware")


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware để log các request và response"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())[:8]
        
        # Start timing
        start_time = time.time()
        
        # Log request
        logger.info(
            f"[{request_id}] {request.method} {request.url.path} - "
            f"Client: {request.client.host if request.client else 'unknown'}"
        )
        
        # Add request ID to request state
        request.state.request_id = request_id
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            logger.info(
                f"[{request_id}] {response.status_code} - "
                f"Processed in {process_time:.3f}s"
            )
            
            # Add headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log error
            logger.error(
                f"[{request_id}] Error processing request: {str(e)} - "
                f"Failed after {process_time:.3f}s"
            )
            
            # Return error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "request_id": request_id,
                    "message": "An unexpected error occurred"
                },
                headers={
                    "X-Request-ID": request_id,
                    "X-Process-Time": str(process_time)
                }
            )


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware để xử lý errors một cách thống nhất"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
            
        except HTTPException as e:
            # HTTP exceptions are handled by FastAPI
            raise e
            
        except ValueError as e:
            logger.warning(f"ValueError in {request.url.path}: {str(e)}")
            return JSONResponse(
                status_code=400,
                content={
                    "error": "Bad Request",
                    "message": str(e),
                    "type": "ValueError"
                }
            )
            
        except FileNotFoundError as e:
            logger.warning(f"FileNotFoundError in {request.url.path}: {str(e)}")
            return JSONResponse(
                status_code=404,
                content={
                    "error": "File Not Found",
                    "message": "The requested file was not found",
                    "type": "FileNotFoundError"
                }
            )
            
        except PermissionError as e:
            logger.error(f"PermissionError in {request.url.path}: {str(e)}")
            return JSONResponse(
                status_code=403,
                content={
                    "error": "Permission Denied",
                    "message": "Insufficient permissions to access the resource",
                    "type": "PermissionError"
                }
            )
            
        except Exception as e:
            # Log full traceback for unexpected errors
            logger.error(
                f"Unexpected error in {request.url.path}: {str(e)}\n"
                f"Traceback: {traceback.format_exc()}"
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal Server Error",
                    "message": "An unexpected error occurred",
                    "type": type(e).__name__
                }
            )


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware để thêm security headers"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Only add HSTS in production
        # response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware"""
    
    def __init__(self, app: ASGIApp, max_requests: int = 100, window_seconds: int = 60):
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # In production, use Redis or similar
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting for health checks and static files
        if request.url.path in ["/health", "/"] or request.url.path.startswith("/static"):
            return await call_next(request)
        
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # Clean old entries
        self.requests = {
            ip: timestamps for ip, timestamps in self.requests.items()
            if any(t > current_time - self.window_seconds for t in timestamps)
        }
        
        # Get current requests for this IP
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        
        # Filter recent requests
        self.requests[client_ip] = [
            t for t in self.requests[client_ip]
            if t > current_time - self.window_seconds
        ]
        
        # Check rate limit
        if len(self.requests[client_ip]) >= self.max_requests:
            logger.warning(f"Rate limit exceeded for {client_ip}")
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Too Many Requests",
                    "message": f"Rate limit exceeded. Max {self.max_requests} requests per {self.window_seconds} seconds.",
                    "retry_after": self.window_seconds
                },
                headers={"Retry-After": str(self.window_seconds)}
            )
        
        # Add current request
        self.requests[client_ip].append(current_time)
        
        return await call_next(request)


class FileUploadMiddleware(BaseHTTPMiddleware):
    """Middleware để xử lý file upload"""
    
    def __init__(self, app: ASGIApp, max_file_size: int = 50 * 1024 * 1024):  # 50MB
        super().__init__(app)
        self.max_file_size = max_file_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Only check upload endpoints
        if not (request.url.path.startswith("/api/upload") and request.method == "POST"):
            return await call_next(request)
        
        # Check content length
        content_length = request.headers.get("content-length")
        if content_length:
            content_length = int(content_length)
            if content_length > self.max_file_size:
                logger.warning(f"File too large: {content_length} bytes (max: {self.max_file_size})")
                return JSONResponse(
                    status_code=413,
                    content={
                        "error": "File Too Large",
                        "message": f"File size exceeds maximum allowed size of {self.max_file_size / (1024*1024):.1f}MB",
                        "max_size_mb": self.max_file_size / (1024*1024)
                    }
                )
        
        return await call_next(request)
