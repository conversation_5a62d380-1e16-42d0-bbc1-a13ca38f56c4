from moviepy.editor import VideoFileClip
import cv2
import numpy as np
from typing import Optional, Dict, Any, List
import logging
import asyncio
import os
import tempfile
from PIL import Image

logger = logging.getLogger(__name__)


class VideoProcessor:
    """Processor để xử lý file video (.mp4, .avi, .mov)"""
    
    async def extract_content(self, file_path: str) -> str:
        """
        Trích xuất thông tin từ file video
        
        Args:
            file_path: Đường dẫn đến file video
            
        Returns:
            str: Thông tin về video và nội dung đã phân tích
        """
        try:
            content_parts = []
            
            # L<PERSON><PERSON> thông tin cơ bản về video
            video_info = await self._get_video_info(file_path)
            content_parts.append("=== Thông tin file video ===")
            for key, value in video_info.items():
                content_parts.append(f"{key}: {value}")
            
            # Phân tích frames
            frame_analysis = await self._analyze_frames(file_path)
            if frame_analysis:
                content_parts.append("\n=== Phân tích nội dung video ===")
                content_parts.append(frame_analysis)
            
            # Trích xuất audio nếu có
            audio_content = await self._extract_audio_content(file_path)
            if audio_content:
                content_parts.append("\n=== Nội dung audio từ video ===")
                content_parts.append(audio_content)
            
            return "\n".join(content_parts)
            
        except Exception as e:
            logger.error(f"Error extracting content from video {file_path}: {str(e)}")
            raise e

    async def _get_video_info(self, file_path: str) -> Dict[str, Any]:
        """Lấy thông tin cơ bản về file video"""
        try:
            def get_info():
                # Sử dụng OpenCV để lấy thông tin cơ bản
                cap = cv2.VideoCapture(file_path)
                
                if not cap.isOpened():
                    return {"Lỗi": "Không thể mở file video"}
                
                # Lấy thông tin video
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                duration = frame_count / fps if fps > 0 else 0
                
                cap.release()
                
                # Thông tin file
                file_size = os.path.getsize(file_path) / (1024*1024)  # MB
                
                return {
                    "Thời lượng": f"{duration:.2f} giây",
                    "Độ phân giải": f"{width}x{height}",
                    "FPS": f"{fps:.2f}",
                    "Tổng số frame": frame_count,
                    "Kích thước file": f"{file_size:.2f} MB",
                    "Codec": "Không xác định"  # Có thể cải thiện sau
                }
            
            return await asyncio.to_thread(get_info)
            
        except Exception as e:
            logger.error(f"Error getting video info: {str(e)}")
            return {"Lỗi": "Không thể lấy thông tin video"}

    async def _analyze_frames(self, file_path: str, sample_count: int = 10) -> str:
        """Phân tích một số frame mẫu từ video"""
        try:
            def analyze():
                cap = cv2.VideoCapture(file_path)
                
                if not cap.isOpened():
                    return "Không thể phân tích frames"
                
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                # Lấy frames ở các thời điểm khác nhau
                frame_indices = np.linspace(0, frame_count-1, min(sample_count, frame_count), dtype=int)
                
                analysis_parts = []
                brightness_values = []
                motion_values = []
                prev_frame = None
                
                for i, frame_idx in enumerate(frame_indices):
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                    ret, frame = cap.read()
                    
                    if not ret:
                        continue
                    
                    # Phân tích độ sáng
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    brightness = np.mean(gray)
                    brightness_values.append(brightness)
                    
                    # Phân tích chuyển động (so với frame trước)
                    if prev_frame is not None:
                        diff = cv2.absdiff(gray, prev_frame)
                        motion = np.mean(diff)
                        motion_values.append(motion)
                    
                    prev_frame = gray.copy()
                
                cap.release()
                
                # Tổng hợp phân tích
                if brightness_values:
                    avg_brightness = np.mean(brightness_values)
                    analysis_parts.append(f"Độ sáng trung bình: {avg_brightness:.1f}/255")
                    
                    if avg_brightness > 180:
                        analysis_parts.append("Loại: Video sáng")
                    elif avg_brightness > 100:
                        analysis_parts.append("Loại: Video độ sáng trung bình")
                    else:
                        analysis_parts.append("Loại: Video tối")
                
                if motion_values:
                    avg_motion = np.mean(motion_values)
                    analysis_parts.append(f"Mức độ chuyển động: {avg_motion:.1f}")
                    
                    if avg_motion > 30:
                        analysis_parts.append("Đặc điểm: Video có nhiều chuyển động")
                    elif avg_motion > 10:
                        analysis_parts.append("Đặc điểm: Video có chuyển động vừa phải")
                    else:
                        analysis_parts.append("Đặc điểm: Video ít chuyển động/tĩnh")
                
                analysis_parts.append(f"Đã phân tích {len(frame_indices)} frames mẫu")
                
                return "\n".join(analysis_parts)
            
            return await asyncio.to_thread(analyze)
            
        except Exception as e:
            logger.error(f"Error analyzing frames: {str(e)}")
            return "Không thể phân tích frames"

    async def _extract_audio_content(self, file_path: str) -> Optional[str]:
        """Trích xuất và transcribe audio từ video"""
        try:
            def extract_audio():
                # Sử dụng moviepy để trích xuất audio
                video = VideoFileClip(file_path)
                
                if video.audio is None:
                    return "Video không có audio"
                
                # Tạo file audio tạm thời
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                    temp_audio_path = temp_audio.name
                
                try:
                    # Export audio
                    video.audio.write_audiofile(temp_audio_path, verbose=False, logger=None)
                    video.close()
                    
                    # Sử dụng AudioProcessor để transcribe
                    from app.processors.audio_processor import AudioProcessor
                    audio_processor = AudioProcessor()
                    
                    # Chỉ lấy transcription, không cần thông tin chi tiết
                    transcription = asyncio.run(audio_processor._transcribe_audio(temp_audio_path))
                    
                    return transcription if transcription else "Không thể transcribe audio từ video"
                    
                finally:
                    # Cleanup
                    if os.path.exists(temp_audio_path):
                        os.unlink(temp_audio_path)
            
            return await asyncio.to_thread(extract_audio)
            
        except Exception as e:
            logger.error(f"Error extracting audio from video: {str(e)}")
            return None

    async def validate_file(self, file_path: str) -> bool:
        """Kiểm tra file video có hợp lệ không"""
        try:
            def validate():
                try:
                    cap = cv2.VideoCapture(file_path)
                    is_valid = cap.isOpened() and cap.get(cv2.CAP_PROP_FRAME_COUNT) > 0
                    cap.release()
                    return is_valid
                except:
                    return False
            
            return await asyncio.to_thread(validate)
            
        except Exception:
            return False

    async def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Lấy metadata của file video"""
        try:
            def get_meta():
                cap = cv2.VideoCapture(file_path)
                
                if not cap.isOpened():
                    return {"frame_count": 0, "duration": 0}
                
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                duration = frame_count / fps if fps > 0 else 0
                
                cap.release()
                
                return {
                    "duration_seconds": duration,
                    "fps": fps,
                    "frame_count": frame_count,
                    "width": width,
                    "height": height,
                    "resolution": f"{width}x{height}",
                    "file_size_mb": os.path.getsize(file_path) / (1024*1024)
                }
            
            return await asyncio.to_thread(get_meta)
            
        except Exception as e:
            logger.error(f"Error getting video metadata: {str(e)}")
            return {"frame_count": 0, "duration": 0}

    async def extract_thumbnail(self, file_path: str, timestamp: float = 1.0) -> Optional[str]:
        """Trích xuất thumbnail từ video tại thời điểm cụ thể"""
        try:
            def extract():
                cap = cv2.VideoCapture(file_path)
                
                if not cap.isOpened():
                    return None
                
                # Set position to timestamp
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_number = int(timestamp * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                
                ret, frame = cap.read()
                cap.release()
                
                if not ret:
                    return None
                
                # Save thumbnail
                thumbnail_path = file_path.replace(os.path.splitext(file_path)[1], "_thumbnail.jpg")
                cv2.imwrite(thumbnail_path, frame)
                
                return thumbnail_path
            
            return await asyncio.to_thread(extract)
            
        except Exception as e:
            logger.error(f"Error extracting thumbnail: {str(e)}")
            return None
